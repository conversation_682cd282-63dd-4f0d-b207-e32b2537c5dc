const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 我是桥梁preload.js
console.log('预加载脚本已执行')
// 安全地暴露API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 获取系统用户名
    getUsername: () => ipcRenderer.invoke('get-username'),

    // 获取本地图片的URL
    getLocalImageUrl: async (imagePath) => {
        try {
            // 调用主进程的方法获取图片URL
            const url = await ipcRenderer.invoke('get-local-image', imagePath)
            return url
        } catch (error) {
            console.error('Failed to get local image URL:', error)
            return null
        }
    },

    // 检查文件是否存在
    fileExists: async (filePath) => {
        try {
            return await ipcRenderer.invoke('file-exists', filePath)
        } catch (error) {
            console.error('Failed to check if file exists:', error)
            return false
        }
    },

    // 获取图片的 data URL
    getImageDataUrl: async (imagePath) => {
        try {
            return await ipcRenderer.invoke('get-image-data-url', imagePath)
        } catch (error) {
            console.error('Failed to get image data URL:', error)
            return null
        }
    },

    // 获取图片的 JPEG 格式
    getLocalImageAsJpeg: async (imagePath) => {
        try {
            return await ipcRenderer.invoke('get-local-image-as-jpeg', imagePath)
        } catch (error) {
            console.error('Failed to get image as JPEG:', error)
            return null
        }
    },

    // 清除图片缓存
    clearImageCache: async (imagePath) => {
        try {
            return await ipcRenderer.invoke('clear-image-cache', imagePath)
        } catch (error) {
            console.error('Failed to clear image cache:', error)
            return false
        }
    },

    // 开始监听指定目录
    startWatchingDirectory: async (directoryPath) => {
        try {
            return await ipcRenderer.invoke('start-watching-directory', directoryPath)
        } catch (error) {
            console.error('Failed to start watching directory:', error)
            return false
        }
    },

    // 获取指定目录中的最新TIFF文件
    getLatestTiffFile: async (directoryPath) => {
        try {
            return await ipcRenderer.invoke('get-latest-tiff-file', directoryPath)
        } catch (error) {
            console.error('Failed to get latest TIFF file:', error)
            return null
        }
    },

    // 获取当前缓存的最新图片路径
    getCachedLatestImage: async (directoryPath) => {
        try {
            return await ipcRenderer.invoke('get-cached-latest-image', directoryPath)
        } catch (error) {
            console.error('Failed to get cached latest image:', error)
            return null
        }
    },

    // 清除图片缓存
    clearImageCache: async (imagePath) => {
        try {
            return await ipcRenderer.invoke('clear-image-cache', imagePath)
        } catch (error) {
            console.error('Failed to clear image cache:', error)
            return false
        }
    },

    // 监听新图片事件
    onNewImageDetected: (callback) => {
        ipcRenderer.on('new-image-detected', (_, imagePath) => {
            callback(imagePath)
        })
    },

    // 监听图片删除事件
    onImageDeleted: (callback) => {
        ipcRenderer.on('image-deleted', (_, imagePath) => {
            callback(imagePath)
        })
    },

    // 移除新图片事件监听器
    removeNewImageListener: () => {
        ipcRenderer.removeAllListeners('new-image-detected')
    },

    // 保存图片路径到本地存储
    saveImagePaths: async (imagePath1, imagePath2, timeStep1, timeStep2) => {
        try {
            return await ipcRenderer.invoke('save-image-paths', imagePath1, imagePath2, timeStep1, timeStep2)
        } catch (error) {
            console.error('Failed to save image paths:', error)
            return false
        }
    },

    // 从本地存储加载图片路径
    loadImagePaths: async () => {
        try {
            return await ipcRenderer.invoke('load-image-paths')
        } catch (error) {
            console.error('Failed to load image paths:', error)
            return null
        }
    },

    // 注册加载保存的图片事件监听器
    onLoadSavedImages: (callback) => {
        ipcRenderer.on('load-saved-images', (_, data) => {
            console.log('接收到保存的图片路径:', data)
            callback(data)
        })
    },

    // 移除加载保存的图片事件监听器
    removeLoadSavedImagesListener: () => {
        ipcRenderer.removeAllListeners('load-saved-images')
    },

    // 获取目录中的图片文件
    getDirectoryImages: (directoryPath, extensions) => {
        return ipcRenderer.invoke('get-directory-images', directoryPath, extensions)
    },

    // 获取最新的图片文件（支持自定义模式）
    getLatestImageFile: (folderPath, pattern) => {
        return ipcRenderer.invoke('get-latest-image-file', folderPath, pattern)
    }
})
