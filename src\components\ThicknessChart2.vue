<template>
    <div class="chart-wrapper">
        <div v-if="flowRateStore.flowRateHistory.length > 0">
            <div ref="chartContainer" class="chart-container"></div>
        </div>
        <div v-else class="empty-chart">
            <el-empty description="暂无历史数据" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useFlowRateStore } from '../stores/flowRateStore'

// 定义props
const props = defineProps({
    flowRate: {
        type: Number,
        default: 0
    }
})

// 图表容器引用
const chartContainer = ref(null)
// 图表实例
let chartInstance = null

// 使用Pinia store
const flowRateStore = useFlowRateStore()

// 初始化图表
const initChart = () => {
    if (!chartContainer.value) return

    // 如果已经存在图表实例，先销毁
    if (chartInstance) {
        chartInstance.dispose()
    }

    // 创建ECharts实例
    chartInstance = echarts.init(chartContainer.value)

    // 更新图表
    updateChart()

    // 响应窗口变化
    window.addEventListener('resize', () => {
        if (chartInstance) {
            chartInstance.resize()
        }
    })
}

// 更新图表数据
const updateChart = () => {
    if (!chartInstance) return
    if (flowRateStore.flowRateHistory.length === 0) return

    // 准备图表数据
    const times = flowRateStore.flowRateHistory.map(item => item.time)
    const values = flowRateStore.flowRateHistory.map(item => item.value)

    // 设置图表选项
    const option = {
        title: {
            text: '临界流速变化趋势',
            textStyle: {
                color: '#2c3e50'
            }
        },
        tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c} m/s'
        },
        xAxis: {
            type: 'category',
            data: times,
            axisLabel: {
                rotate: 40,
                margin: 9.5
            }
        },
        yAxis: {
            type: 'value',
            name: '流速(m/s)',
            nameTextStyle: {
                padding: [0, 0, 0, 40]
            }
        },
        series: [
            {
                name: '临界流速',
                type: 'line',
                data: values,
                smooth: true,
                lineStyle: {
                    width: 3,
                    color: '#e74c3c'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(231, 76, 60, 0.5)' },
                        { offset: 1, color: 'rgba(231, 76, 60, 0.1)' }
                    ])
                },
                markPoint: {
                    data: [
                        { type: 'max', name: '最大值' },
                        { type: 'min', name: '最小值' }
                    ]
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '15%'
        }
    }

    // 应用配置项
    chartInstance.setOption(option)
}

// 确保在DOM更新后初始化图表
const ensureChartInitialized = async () => {
    if (flowRateStore.flowRateHistory.length > 0) {
        // 等待DOM更新
        await nextTick()

        // 初始化或更新图表
        if (!chartInstance && chartContainer.value) {
            initChart()
        } else if (chartInstance) {
            updateChart()
        }
    }
}

// 监听历史数据变化
watch(() => flowRateStore.flowRateHistory, () => {
    ensureChartInitialized()
}, { deep: true })

// 也监听历史数据长度变化（确保第一次添加数据时能正确响应）
watch(() => flowRateStore.flowRateHistory.length, (newLength, oldLength) => {
    if (newLength > 0 && oldLength === 0) {
        // 从无数据变为有数据，确保初始化图表
        ensureChartInitialized()
    }
})

// 监听流速变化
watch(() => props.flowRate, (newValue) => {
    if (newValue > 0) {
        // 将新数据添加到store中
        flowRateStore.addFlowRateData(newValue)
    }
})

// 组件挂载后初始化图表
onMounted(() => {
    // 只有在有数据时才初始化图表
    ensureChartInitialized()
})
</script>

<style scoped>
.chart-wrapper {
    height: 100%;
    width: 100%;
}

.chart-container {
    height: 400px;
    width: 100%;
}

.empty-chart {
    height: 400px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>