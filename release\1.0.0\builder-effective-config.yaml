directories:
  output: release/${version}
  buildResources: build
$schema: https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json
appId: YourAppID
asar: true
productName: Sein
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
  artifactName: ${productName}-Windows-${version}-Setup.${ext}
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  deleteAppDataOnUninstall: false
linux:
  target:
    - AppImage
  artifactName: ${productName}-Linux-${version}.${ext}
electronVersion: 30.5.1
