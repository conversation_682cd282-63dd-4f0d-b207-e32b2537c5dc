<template>
  <div class="image-library">
    <!-- 头部控制区域 -->
    <div class="library-header">
      <div class="header-left">
        <el-button @click="$emit('back')" type="text" class="back-button">
          ← 返回数据分析
        </el-button>
        <h2>文件库</h2>
      </div>
      <div class="header-right">
        <!-- <el-button type="primary" @click="showPathConfig = true" class="config-button">
          ⚙️ 配置路径
        </el-button> -->
        <el-button type="success" @click="refreshLibrary" class="refresh-button">
          🔄 刷新
        </el-button>
      </div>
    </div>

    <!-- 当前路径显示 -->
    <div class="current-path">
      <span class="path-label">当前目录:</span>
      <span class="path-value">{{ currentDirectory }}</span>
      <span class="file-count">(共 {{ filteredImages.length }} 个文件)</span>
    </div>

    <!-- 图片网格 -->
    <div class="image-grid" v-if="filteredImages.length > 0">
      <div 
        v-for="(image, index) in filteredImages" 
        :key="index" 
        class="image-item"
        @click="selectImage(index)"
        :class="{ 'selected': selectedIndex === index }"
      >
        <div class="image-thumbnail">
          <img 
            :src="image.url" 
            :alt="image.name"
            @error="handleImageError(index)"
            @load="handleImageLoad(index)"
          />
          <div v-if="image.loading" class="loading-overlay">
            <span>加载中...</span>
          </div>
          <div v-if="image.error && !image.url" class="error-overlay">
            <span>加载失败</span>
          </div>
        </div>
        <div class="image-info">
          <div class="image-name" :title="image.name">{{ image.name }}</div>
          <div class="image-directory" v-if="image.directoryName">{{ image.directoryName }}</div>
          <div class="image-size">{{ formatFileSize(image.size) }}</div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📁</div>
      <h3>暂无图片文件</h3>
      <p>请配置正确的目录路径，或检查目录中是否包含支持的图片格式</p>
      <!-- <el-button type="primary" @click="showPathConfig = true">配置路径</el-button> -->
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog 
      v-model="showPreview" 
      :title="selectedImage?.name || '图片预览'"
      width="80%"
      class="image-preview-dialog"
    >
      <div class="preview-container" v-if="selectedImage">
        <img 
          :src="selectedImage.url" 
          :alt="selectedImage.name"
          class="preview-image"
        />
        <div class="preview-info">
          <p><strong>文件名:</strong> {{ selectedImage.name }}</p>
          <p v-if="selectedImage.directoryDescription"><strong>目录说明:</strong> {{ selectedImage.directoryDescription }}</p>
          <p><strong>文件大小:</strong> {{ formatFileSize(selectedImage.size) }}</p>
          <p><strong>文件路径:</strong> {{ selectedImage.path }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useThicknessStore } from '../stores/thicknessStore'

// 使用store
const thicknessStore = useThicknessStore()

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const showPathConfig = ref(false)
const showPreview = ref(false)
const selectedIndex = ref(-1)
const images = ref([])
const currentDirectory = ref('')
const selectedTemplate = ref('')
// 写死的图片目录路径配置
const imageDirectories = [
  {
    // name: '目录1',
    path: 'C:\\Program Files\\eblow_prediction_version\\application',
    description: '弯头气固冲蚀场实时预测'
  },
  {
    // name: '目录2',
    path: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\YFL\\mcrCache9.14\\run_pr0\\run_predicti\\Predicted_Results',
    description: '预测1时间步数的分析结果'
  },
  {
    // name: '目录3',
    path: 'C:\\Program Files\\GasVelocity_MarkovLSTM_TFA\\application\\figs',
    description: '气速未来状态时序预测'
  },
  {
    // name: '目录4',
    path: 'C:\\Program Files\\CFD_Grey_Markov_Predictor\\application',
    description: '管道壁厚剩余寿命预测与风险评估'
  }
]

// 支持的文件扩展名
const supportedExtensions = ['tiff', 'tif', 'jpg', 'jpeg', 'png']

// 计算属性
const filteredImages = computed(() => {
  console.log(`🔍 开始过滤图片，总数: ${images.value.length}`)

  const filtered = images.value.filter(img => {
    // 过滤条件：
    // 1. 不是splash.png文件
    // 2. 有URL或者正在加载中（不过滤有错误但有URL的图片）
    const isSplash = img.name.toLowerCase().includes('splash.png')
    const hasValidContent = img.url || img.loading
    const shouldShow = !isSplash && hasValidContent
    console.log(`📋 图片: ${img.name}, 目录: ${img.directoryName}, error: ${img.error}, loading: ${img.loading}, isSplash: ${isSplash}, hasUrl: ${!!img.url}, shouldShow: ${shouldShow}`)
    return shouldShow
  })

  console.log(`✅ 过滤完成 - 显示: ${filtered.length}, 总数: ${images.value.length}`)
  console.log(`📊 过滤结果详情:`, filtered.map(img => `${img.name} (${img.directoryName})`))

  return filtered
})

const selectedImage = computed(() => {
  if (selectedIndex.value >= 0 && selectedIndex.value < filteredImages.value.length) {
    return filteredImages.value[selectedIndex.value]
  }
  return null
})

// 方法
const applyTemplate = async (templatePath) => {
  if (templatePath === 'custom') {
    // 自定义路径，不做任何操作
    return
  }

  let finalPath = templatePath

  // 替换用户名占位符
  if (templatePath.includes('{username}')) {
    try {
      const username = await window.electronAPI?.getUsername()
      if (username) {
        finalPath = templatePath.replace('{username}', username)
      }
    } catch (error) {
      console.error('获取用户名失败:', error)
      finalPath = templatePath.replace('{username}', 'YFL') // 使用默认用户名
    }
  }

  pathForm.directory = finalPath
}

const savePathConfig = async () => {
  if (!pathForm.directory.trim()) {
    ElMessage.error('请输入有效的目录路径')
    return
  }

  currentDirectory.value = pathForm.directory

  // 保存配置到本地存储
  const config = {
    directory: pathForm.directory,
    extensions: pathForm.extensions,
    timestamp: Date.now()
  }
  localStorage.setItem('image-library-config', JSON.stringify(config))

  showPathConfig.value = false
  await loadImagesFromAllDirectories()
  ElMessage.success('路径配置已保存')
}

const refreshLibrary = async () => {
  await loadImagesFromAllDirectories()
  ElMessage.success('文件库已刷新')
}

const selectImage = (index) => {
  selectedIndex.value = index
  showPreview.value = true
}

const handleImageError = (index) => {
  if (images.value[index]) {
    const img = images.value[index]
    console.error(`❌ 图片加载失败: ${img.name}, URL前50字符: ${img.url ? img.url.substring(0, 50) : 'null'}`)
    img.error = true
    img.loading = false
  }
}

const handleImageLoad = (index) => {
  if (images.value[index]) {
    const img = images.value[index]
    console.log(`✅ 图片加载成功: ${img.name}`)
    img.loading = false
    img.error = false  // 确保加载成功时清除错误状态
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return '未知大小'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 加载所有目录下的图片
const loadImagesFromAllDirectories = async () => {
  try {
    if (!window.electronAPI?.getDirectoryImages) {
      ElMessage.error('Electron API 不可用')
      return
    }

    let allImages = []
    let loadedCount = 0

    // 遍历所有写死的目录
    for (const directory of imageDirectories) {
      try {
        console.log(`正在加载目录: ${directory.name} - ${directory.path}`)

        const result = await window.electronAPI.getDirectoryImages(
          directory.path,
          supportedExtensions
        )

        if (result.success && result.images.length > 0) {
          // 为每个图片添加目录信息，并异步加载图片数据
          const imagesWithDirectory = result.images.map(img => ({
            ...img,
            loading: true,
            error: false,
            url: '', // 初始为空，稍后异步加载
            directoryName: directory.name,
            directoryDescription: directory.description
          }))

          // 异步加载每个图片的数据URL
          for (let i = 0; i < imagesWithDirectory.length; i++) {
            const img = imagesWithDirectory[i]
            try {
              console.log(`正在加载图片: ${img.name} (${img.extension}) 从目录: ${img.directoryName}`)
              const dataUrl = await window.electronAPI.getImageDataUrl(img.path)
              console.log(`获取到数据URL: ${img.name}, 类型: ${typeof dataUrl}, 长度: ${dataUrl ? dataUrl.length : 0}`)

              if (dataUrl && typeof dataUrl === 'string' && dataUrl.length > 0) {
                img.url = dataUrl
                img.loading = false
                img.error = false  // 明确设置为false
                console.log(`✅ 图片加载成功: ${img.name}`)
              } else {
                console.error(`❌ 图片数据URL无效: ${img.name}`, dataUrl)
                img.error = true
                img.loading = false
              }
            } catch (error) {
              console.error(`❌ 加载图片失败: ${img.path}`, error)
              img.error = true
              img.loading = false
            }
          }

          console.log(`目录 ${directory.name} 加载完成，成功: ${imagesWithDirectory.filter(img => !img.error).length}, 失败: ${imagesWithDirectory.filter(img => img.error).length}`)

          allImages = allImages.concat(imagesWithDirectory)
          loadedCount++
          console.log(`从 ${directory.name} 加载了 ${result.images.length} 个图片`)
        } else {
          console.log(`目录 ${directory.name} 中没有找到图片或加载失败:`, result.error)
        }
      } catch (dirError) {
        console.error(`加载目录 ${directory.name} 时出错:`, dirError)
      }
    }

    // 按修改时间排序（最新的在前）
    allImages.sort((a, b) => new Date(b.modified) - new Date(a.modified))

    images.value = allImages
    currentDirectory.value = `已扫描 ${imageDirectories.length} 个目录`

    if (allImages.length > 0) {
      ElMessage.success(`成功从 ${loadedCount} 个目录加载了 ${allImages.length} 个图片文件`)
    } else {
      ElMessage.warning('所有目录中都没有找到支持的图片文件')
    }

  } catch (error) {
    console.error('加载图片目录失败:', error)
    ElMessage.error('加载图片目录失败: ' + error.message)
    images.value = []
  }
}

// 加载保存的配置
const loadSavedConfig = () => {
  try {
    const savedConfig = localStorage.getItem('image-library-config')
    if (savedConfig) {
      const config = JSON.parse(savedConfig)
      pathForm.directory = config.directory || pathForm.directory
      pathForm.extensions = config.extensions || pathForm.extensions
      currentDirectory.value = pathForm.directory
      console.log('已加载保存的配置:', config)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 设置当前目录显示信息
  currentDirectory.value = '正在扫描预设目录...'
  await loadImagesFromAllDirectories()
})
</script>

<style scoped>
.image-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 10px;
  overflow: hidden;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.back-button {
  color: white !important;
  font-size: 16px;
}

.back-button:hover {
  color: #ecf0f1 !important;
}

.config-button, .refresh-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.config-button:hover, .refresh-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.current-path {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.path-label {
  font-weight: bold;
  color: #495057;
}

.path-value {
  color: #007bff;
  margin-left: 0.5rem;
  font-family: monospace;
}

.file-count {
  color: #6c757d;
  margin-left: 1rem;
}

.image-grid {
  flex: 1;
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  overflow-y: auto;
}

.image-item {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.image-item:hover {
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.image-item.selected {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.image-thumbnail {
  position: relative;
  width: 100%;
  height: 200px; /* 固定高度，创建正方形效果 */
  overflow: hidden;
  background: #f8f9fa;
  border-radius: 8px;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 8px;
}

.image-item:hover .image-thumbnail img {
  transform: scale(1.05);
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.9);
  color: #6c757d;
  font-size: 14px;
  z-index: 1;
}

.error-overlay {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.image-info {
  padding: 1rem;
}

.image-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-directory {
  font-size: 11px;
  color: #007bff;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: rgba(0, 123, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

.image-size {
  font-size: 12px;
  color: #6c757d;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  padding: 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-state p {
  margin-bottom: 1.5rem;
  text-align: center;
  line-height: 1.5;
}

.path-config {
  padding: 1rem 0;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-info {
  margin-top: 1rem;
  text-align: left;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.preview-info p {
  margin-bottom: 0.5rem;
  color: #495057;
}

.preview-info strong {
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .library-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-left, .header-right {
    justify-content: center;
  }
  
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
    padding: 1rem;
  }

  .image-thumbnail {
    height: 150px; /* 移动端稍小的正方形 */
  }
}
</style>
