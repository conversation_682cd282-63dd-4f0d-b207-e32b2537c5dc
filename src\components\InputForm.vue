<template>
  <div class="input-form">
    <el-form :model="formData">
      <el-form-item label="管道倾斜角(°)：">
        <el-input v-model="formData.inclinationAngle" type="number" placeholder="请输入管道倾斜角"></el-input>
      </el-form-item>

      <el-form-item label="管内流速(m/s)：">
        <el-input v-model="formData.flowRate" type="number" placeholder="请输入流速"></el-input>
      </el-form-item>

      <el-form-item label="管内含水量(%)：">
        <el-input v-model="formData.waterContent" type="number" placeholder="请输入含水量（0-1之间）"></el-input>
        <span class="input-tip">请输入0到1之间的小数，例如：0.25表示25%</span>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="calculateThickness">计算积液厚度</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="result-panel" v-if="showResult">
      <h3>计算结果</h3>
      <p class="result-value">管道积液厚度：{{ calculatedThickness }} mm</p>
      <div class="formula-explanation">
        <h4>计算公式：</h4>
        <p>y = 0.5121 + 3.475x₁ + 2.1715x₂ + 5648.7847x₃ - 0.7712x₁x₂ + 655.5461x₁x₃ - 1179.5383x₂x₃ - 0.0542x₁² -
          0.4452x₂² - 330811.8291x₃²</p>
        <p>其中：x₁为管道倾斜角，x₂为管内流速，x₃为管内含水量</p>
      </div>

      <div class="action-buttons">
        <el-button size="small" type="danger" @click="clearHistoryAndRefresh">
          清空历史记录
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useThicknessStore } from '../stores/thicknessStore'

// 使用store
const thicknessStore = useThicknessStore()

// 定义表单数据
const formData = reactive({
  inclinationAngle: '', // 管道倾斜角 x₁
  flowRate: '',         // 管内流速 x₂
  waterContent: ''      // 管内含水量 x₃
})

// 计算结果相关变量
const calculatedThickness = ref(0)
const showResult = ref(false)

// 清空历史记录并刷新页面的方法
const clearHistoryAndRefresh = () => {
  // 先清空历史记录
  thicknessStore.clearHistory()
  // 然后刷新页面
  window.location.reload()
}

// 计算积液厚度的方法
//方法一前端处理
const calculateThickness = () => {
  // 获取输入值
  const x1 = parseFloat(formData.inclinationAngle)  // 管道倾斜角
  const x2 = parseFloat(formData.flowRate)          // 管内流速
  const x3 = parseFloat(formData.waterContent)      // 管内含水量

  // 验证输入
  if (isNaN(x1) || isNaN(x2) || isNaN(x3)) {
    alert('请输入有效的数值')
    return
  }

  // 使用给定公式计算积液厚度
  const thickness = 0.5121
    + 3.475 * x1                  // 管道倾斜角项
    + 2.1715 * x2                 // 管内流速项
    + 5648.7847 * x3              // 管内含水量项
    - 0.7712 * x1 * x2            // 管道倾斜角×管内流速项
    + 655.5461 * x1 * x3          // 管道倾斜角×管内含水量项
    - 1179.5383 * x2 * x3         // 管内流速×管内含水量项
    - 0.0542 * Math.pow(x1, 2)    // 管道倾斜角平方项
    - 0.4452 * Math.pow(x2, 2)    // 管内流速平方项
    - 330811.8291 * Math.pow(x3, 2) // 管内含水量平方项

  // 使用完整计算结果
  calculatedThickness.value = thickness

  showResult.value = true

  // 触发事件，通知父组件更新图表
  emit('update-chart', thickness)
}
//方法二后端处理
// const calculateThickness = async() => {
//   try {
//     // 获取输入值
//     const x1 = parseFloat(formData.inclinationAngle)  // 管道倾斜角
//     const x2 = parseFloat(formData.flowRate)          // 管内流速
//     const x3 = parseFloat(formData.waterContent)      // 管内含水量

//     // 验证输入
//     if (isNaN(x1) || isNaN(x2) || isNaN(x3)) {
//       ElMessage.error('请输入有效的数值')
//       return
//     }

//     // 验证含水量范围
//     if (x3 < 0 || x3 > 1) {
//       ElMessage.error('含水量必须在0到1之间')
//       return
//     }

//     // 后端API地址
//     // 构建URL参数
//     const params = new URLSearchParams({
//       angle: x1,
//       flow: x2,
//       water: x3
//     });

//     const response = await fetch(`http://localhost:3000/api/calculate?${params}`);

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`)
//     }

//     const data = await response.json();

//     if (data.error) {
//       ElMessage.error(data.error)
//       return
//     }

//     // 从后端返回的数据中提取厚度值
//     calculatedThickness.value = data.thickness

//     showResult.value = true

//     // 触发事件，通知父组件更新图表
//     emit('update-chart', data.thickness)

//   } catch (error) {
//     console.error('计算错误:', error)
//     ElMessage.error('计算过程中出现错误，请检查输入或稍后重试')
//     // 确保在错误时不显示结果
//     showResult.value = false
//   }
// }
// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  showResult.value = false
}

// 定义组件事件
const emit = defineEmits(['update-chart'])
</script>

<style scoped>
.input-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.result-panel {
  background: #edf7fd;
  border-left: 4px solid #3498db;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.result-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3498db;
  margin-top: 0.5rem;
}

h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.input-tip {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.2rem;
}

.formula-explanation {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed #bbb;
}

.formula-explanation h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.formula-explanation p {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
}

.action-buttons {
  margin-top: 1rem;
  text-align: right;
}
</style>