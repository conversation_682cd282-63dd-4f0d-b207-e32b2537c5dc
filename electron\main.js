const { app, BrowserWindow, ipcMain, protocol } = require('electron')
const path = require('path')
const { spawn } = require('child_process');
const os = require('os')
const fs = require('fs')
const sharp = require('sharp')
const crypto = require('crypto')
const chokidar = require('chokidar') // 添加文件监听库
const electron = require('electron')
const userDataPath = (electron.app || electron.remote.app).getPath('userData')

// 全局变量来存储监听器和最新图片路径
let watchers = {} // 使用对象存储多个监听器
let latestImagePaths = {} // 使用对象存储每个目录的最新图片路径

// 存储图片路径的文件路径
const imagePathsFile = path.join(userDataPath, 'imagePaths.json')
//后端server路径配置
const serverPath = path.join(__dirname, '../server/server.js');

// 全局变量来存储当前显示的图片路径
let currentImagePath1 = ''
let currentImagePath2 = ''
let currentTimeStep1 = ''
let currentTimeStep2 = ''

// 保存图片路径到文件
function saveImagePathsToFile(imagePath1, imagePath2, timeStep1, timeStep2) {
    try {
        console.log(`尝试保存图片路径到文件: ${imagePathsFile}`);
        console.log(`要保存的路径: ${imagePath1}, ${imagePath2}`);
        console.log(`要保存的时间步数: ${timeStep1}, ${timeStep2}`);

        // 验证路径是否存在
        let imagePath1Exists = false;
        let imagePath2Exists = false;

        if (imagePath1) {
            try {
                imagePath1Exists = fs.existsSync(imagePath1);
                console.log(`第一张图片文件是否存在: ${imagePath1Exists}`);
            } catch (error) {
                console.error('检查第一张图片文件是否存在时出错:', error);
            }
        }

        if (imagePath2) {
            try {
                imagePath2Exists = fs.existsSync(imagePath2);
                console.log(`第二张图片文件是否存在: ${imagePath2Exists}`);
            } catch (error) {
                console.error('检查第二张图片文件是否存在时出错:', error);
            }
        }

        // 只保存存在的文件路径
        const data = {
            imagePath1: imagePath1Exists ? imagePath1 : '',
            imagePath2: imagePath2Exists ? imagePath2 : '',
            timeStep1: imagePath1Exists ? timeStep1 : '',
            timeStep2: imagePath2Exists ? timeStep2 : '',
            timestamp: Date.now()
        }

        const jsonData = JSON.stringify(data, null, 2);
        console.log(`要写入文件的数据: ${jsonData}`);

        fs.writeFileSync(imagePathsFile, jsonData);
        console.log(`保存图片路径到文件成功: ${imagePathsFile}`);

        // 验证文件是否写入成功
        if (fs.existsSync(imagePathsFile)) {
            const fileContent = fs.readFileSync(imagePathsFile, 'utf8');
            console.log(`验证文件内容: ${fileContent}`);
        }

        return true;
    } catch (error) {
        console.error('保存图片路径失败:', error);
        return false;
    }
}

// 从文件加载图片路径
function loadImagePathsFromFile() {
    try {
        console.log(`尝试从文件加载图片路径: ${imagePathsFile}`);

        if (!fs.existsSync(imagePathsFile)) {
            console.log(`图片路径文件不存在: ${imagePathsFile}`)
            return null
        }

        const fileContent = fs.readFileSync(imagePathsFile, 'utf8');
        console.log(`文件内容: ${fileContent}`);

        const data = JSON.parse(fileContent);
        console.log(`从文件加载图片路径: ${imagePathsFile}`);
        console.log(`加载的数据: ${JSON.stringify(data)}`);

        // 验证数据
        if (!data.imagePath1 && !data.imagePath2) {
            console.warn('加载的数据不包含图片路径');
        }

        return data;
    } catch (error) {
        console.error('加载图片路径失败:', error)
        return null
    }
}

// 创建图像处理服务
// 注册本地文件协议
function registerLocalFileProtocol() {
    // 使用新的 protocol.handle API
    if (protocol.handle) {
        // 注册自定义协议
        protocol.handle('local-file', async (request) => {
            try {
                // 从 URL 中提取文件路径
                const url = new URL(request.url)
                let filePath = decodeURIComponent(url.pathname)

                // 在 Windows 上处理路径
                if (process.platform === 'win32') {
                    // 移除开头的斜杠
                    filePath = filePath.startsWith('/') ? filePath.slice(1) : filePath
                    // 将正斜杠转换为反斜杠
                    filePath = filePath.replace(/\//g, '\\')
                }

                console.log('Protocol handling file path:', filePath)

                // 安全验证
                if (!isValidLocalPath(filePath)) {
                    console.error('Access denied to path:', filePath)
                    return new Response('Access denied', { status: 403 })
                }

                // 检查文件是否存在
                if (!fs.existsSync(filePath)) {
                    console.error('File not found:', filePath)
                    return new Response('File not found', { status: 404 })
                }

                // 获取文件扩展名
                const ext = path.extname(filePath).toLowerCase()

                // 根据文件类型设置 MIME 类型
                let mimeType = ''
                if (ext === '.tiff' || ext === '.tif') {
                    mimeType = 'image/tiff'
                } else if (ext === '.jpg' || ext === '.jpeg') {
                    mimeType = 'image/jpeg'
                } else if (ext === '.png') {
                    mimeType = 'image/png'
                } else if (ext === '.gif') {
                    mimeType = 'image/gif'
                } else if (ext === '.bmp') {
                    mimeType = 'image/bmp'
                }

                // 读取文件内容
                const fileData = fs.readFileSync(filePath)

                // 返回文件内容和 MIME 类型
                return new Response(fileData, {
                    status: 200,
                    headers: {
                        'Content-Type': mimeType || 'application/octet-stream'
                    }
                })
            } catch (error) {
                console.error('Protocol error:', error)
                return new Response(`Error: ${error.message}`, { status: 500 })
            }
        })

        console.log('Registered local-file protocol using protocol.handle')
    } else {
        // 兼容旧版本
        protocol.registerFileProtocol('local-file', (request, callback) => {
            try {
                // 从 URL 中提取文件路径
                const url = request.url.slice(12) // 移除 'local-file://'
                const decodedUrl = decodeURI(url)

                console.log('Legacy protocol handling file path:', decodedUrl)

                // 安全验证
                if (!isValidLocalPath(decodedUrl)) {
                    throw new Error('Access denied to specified path')
                }

                // 获取文件扩展名
                const ext = path.extname(decodedUrl).toLowerCase()

                // 根据文件类型设置 MIME 类型
                let mimeType = ''
                if (ext === '.tiff' || ext === '.tif') {
                    mimeType = 'image/tiff'
                } else if (ext === '.jpg' || ext === '.jpeg') {
                    mimeType = 'image/jpeg'
                } else if (ext === '.png') {
                    mimeType = 'image/png'
                } else if (ext === '.gif') {
                    mimeType = 'image/gif'
                } else if (ext === '.bmp') {
                    mimeType = 'image/bmp'
                }

                // 返回文件路径和 MIME 类型
                return callback({
                    path: decodedUrl,
                    mimeType: mimeType || undefined
                })
            } catch (error) {
                console.error('Protocol error:', error)
                return callback(404)
            }
        })

        console.log('Registered local-file protocol using legacy API')
    }
}

// 路径安全验证函数
function isValidLocalPath(filePath) {
    const normalized = path.normalize(filePath)

    // 获取系统临时目录和用户目录
    const tempDir = os.tmpdir()
    const userDir = os.homedir()

    // 允许的目录前缀
    const allowedPrefixes = [
        tempDir,
        userDir,
        'C:\\Users\\<USER>\\Program Files\\', // Windows 程序文件目录
        'C:\\Program Files (x86)\\' // Windows 32位程序文件目录
    ]

    // 验证路径是否在允许的目录下
    return allowedPrefixes.some(prefix =>
        normalized.startsWith(prefix) &&
        !normalized.includes('..') &&
        path.isAbsolute(normalized)
    )
}

// 创建缓存目录
const cacheDir = path.join(os.tmpdir(), 'electron-image-cache')
if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true })
}

// 将图片转换为 data URL
async function getImageAsDataUrl(imagePath) {
    try {
        console.log('Getting data URL for image:', imagePath)

        // 验证路径
        if (!isValidLocalPath(imagePath) || !fs.existsSync(imagePath)) {
            throw new Error('Invalid image path or file does not exist')
        }

        // 获取文件扩展名
        const ext = path.extname(imagePath).toLowerCase()

        // 如果是 TIFF 格式，先转换为 JPEG
        if (ext === '.tiff' || ext === '.tif') {
            console.log('TIFF format detected, converting to JPEG first')
            const hash = crypto.createHash('md5').update(imagePath).digest('hex')
            const jpegPath = path.join(cacheDir, `${hash}_direct.jpg`)

            // 如果缓存不存在，创建新的 JPEG
            if (!fs.existsSync(jpegPath)) {
                try {
                    await sharp(imagePath)
                        .jpeg({ quality: 90 })
                        .toFile(jpegPath)
                    console.log('Converted TIFF to JPEG for data URL:', jpegPath)
                } catch (error) {
                    console.error('Error converting TIFF to JPEG:', error)
                    // 返回一个默认的数据 URL
                    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAADICAYAAADGFbfiAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QMZCgUZYsC0CgAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAABSklEQVR42u3VQQkAIBQFQcVgMBgMBoPBYDAYDQaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDIYXZwDXTQrEs/82iQAAAABJRU5ErkJggg=='
                }
            }

            // 使用新的 JPEG 路径，并确保文件存在
            if (fs.existsSync(jpegPath)) {
                imagePath = jpegPath
                console.log('Using converted JPEG file:', jpegPath)
            } else {
                console.error('Converted JPEG file does not exist:', jpegPath)
                return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAADICAYAAADGFbfiAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QMZCgUZYsC0CgAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAABSklEQVR42u3VQQkAIBQFQcVgMBgMBoPBYDAYDQaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDIYXZwDXTQrEs/82iQAAAABJRU5ErkJggg=='
            }
        }

        // 读取文件
        console.log(`Reading file for data URL: ${imagePath}`)
        const data = fs.readFileSync(imagePath)
        console.log(`File size: ${data.length} bytes, first 4 bytes: ${data.slice(0, 4).toString('hex')}`)

        // 获取文件扩展名（可能已经更新）
        const finalExt = path.extname(imagePath).toLowerCase()
        console.log(`Final extension: ${finalExt}`)

        // 确定 MIME 类型
        let mimeType = 'application/octet-stream'
        if (finalExt === '.png') {
            mimeType = 'image/png'
        } else if (finalExt === '.jpg' || finalExt === '.jpeg') {
            mimeType = 'image/jpeg'
        } else if (finalExt === '.gif') {
            mimeType = 'image/gif'
        } else if (finalExt === '.tiff' || finalExt === '.tif') {
            // 如果还是 TIFF，则使用 JPEG MIME 类型（因为我们已经转换了）
            mimeType = 'image/jpeg'
        }

        // 如果原始文件是TIFF但现在使用的是转换后的JPEG，确保使用正确的MIME类型
        if ((ext === '.tiff' || ext === '.tif') && imagePath.endsWith('.jpg')) {
            mimeType = 'image/jpeg'
        }

        // 转换为 Base64
        const base64Data = data.toString('base64')

        // 返回 data URL
        const dataUrl = `data:${mimeType};base64,${base64Data}`
        console.log(`Generated data URL with MIME type ${mimeType} (first 50 chars):`, dataUrl.substring(0, 50))
        return dataUrl
    } catch (error) {
        console.error('Error converting image to data URL:', error)
        // 返回一个默认的数据 URL
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAADICAYAAADGFbfiAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QMZCgUZYsC0CgAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAABSklEQVR42u3VQQkAIBQFQcVgMBgMBoPBYDAYDQaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDIYXZwDXTQrEs/82iQAAAABJRU5ErkJggg=='
    }
}

// 将 TIFF 转换为 JPEG 并返回 data URL
async function convertTiffToJpegDataUrl(tiffPath) {
    try {
        console.log('Converting TIFF to JPEG data URL:', tiffPath)

        // 生成哈希作为缓存文件名，添加时间戳避免缓存
        const timestamp = Date.now()
        const hash = crypto.createHash('md5').update(tiffPath + timestamp).digest('hex')
        const jpegPath = path.join(cacheDir, `${hash}.jpg`)

        // 强制刷新，不使用缓存
        // 如果缓存文件存在，删除它
        if (fs.existsSync(jpegPath)) {
            try {
                fs.unlinkSync(jpegPath)
                console.log(`删除旧的缓存文件: ${jpegPath}`)
            } catch (error) {
                console.error(`删除缓存文件失败: ${error}`)
            }
        }

        // 转换 TIFF 为 JPEG
        try {
            // 尝试使用 sharp 转换
            await sharp(tiffPath)
                .jpeg({ quality: 90 })
                .toFile(jpegPath)

            console.log('Converted TIFF to JPEG using sharp:', jpegPath)
        } catch (sharpError) {
            console.error('Sharp conversion failed:', sharpError)

            // 如果 sharp 失败，创建一个简单的占位图片
            // 这是一个最小的有效 JPEG 文件
            const minimalJpeg = Buffer.from([
                0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48,
                0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x0b, 0x08, 0x00, 0x01, 0x00,
                0x01, 0x01, 0x01, 0x11, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x10,
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0xff, 0xda, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3f, 0x00, 0x37, 0xff, 0xd9
            ]);

            // 写入文件
            fs.writeFileSync(jpegPath, minimalJpeg);

            console.log('Created minimal JPEG fallback image:', jpegPath);
        }

        // 返回 data URL
        const dataUrl = await getImageAsDataUrl(jpegPath)
        console.log('Generated data URL from new JPEG (first 100 chars):', dataUrl?.substring(0, 100))
        return dataUrl
    } catch (error) {
        console.error('Error converting TIFF to JPEG data URL:', error)
        return null
    }
}

// 转换TIFF图片为PNG
async function convertTiffToPng(tiffPath) {
    try {
        // 生成哈希作为缓存文件名
        const hash = crypto.createHash('md5').update(tiffPath).digest('hex')
        const outputPath = path.join(cacheDir, `${hash}.png`)

        // 检查缓存
        if (fs.existsSync(outputPath)) {
            console.log('Using cached PNG:', outputPath)
            return outputPath
        }

        // 转换TIFF为PNG
        await sharp(tiffPath)
            .png()
            .toFile(outputPath)

        console.log('Converted TIFF to PNG:', outputPath)
        return outputPath
    } catch (error) {
        console.error('Error converting TIFF to PNG:', error)
        return null
    }
}

// 处理获取本地图片的IPC请求
ipcMain.handle('get-local-image', async (_, imagePath) => {
    try {
        // 验证路径安全性
        if (!isValidLocalPath(imagePath)) {
            throw new Error('Invalid image path')
        }

        // 检查文件是否存在
        if (!fs.existsSync(imagePath)) {
            throw new Error('File does not exist')
        }

        // 获取文件扩展名
        const ext = path.extname(imagePath).toLowerCase()

        // 如果是TIFF格式，转换为PNG
        if (ext === '.tiff' || ext === '.tif') {
            const pngPath = await convertTiffToPng(imagePath)
            if (pngPath) {
                // 使用正斜杠并确保路径格式正确
                const formattedPath = pngPath.replace(/\\/g, '/')
                console.log('Formatted PNG path:', formattedPath)
                return `local-file://${formattedPath}`
            }
        }

        // 其他格式直接返回，同样使用正斜杠
        const formattedPath = imagePath.replace(/\\/g, '/')
        return `local-file://${formattedPath}`
    } catch (error) {
        console.error('Error accessing local image:', error)
        return null
    }
})



function createWindow() {
    //当app准备好后，执行createWindow创建窗口
    console.log('创建主窗口')
    mainWindow = new BrowserWindow({
        width: 1184,
        height: 750,
        // minWidth: 1291,  // 最小宽度
        // minHeight: 383, // 最小高度
        minWidth: 1080,  // 最小宽度
        minHeight: 600, // 最小高度
        autoHideMenuBar: true, // 显示菜单栏以便调试
        useContentSize: true, // 使用内容区域大小
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            contextIsolation: true,  // 必须启用上下文隔离
            nodeIntegration: false,
            // 必需的安全设置
            webSecurity: true,  // 保持启用
            sandbox: true,
            contentSecurityPolicy: `
      default-src 'self' local-file: https:;
      img-src 'self' data: local-file: https: file:;
      script-src 'self' 'unsafe-inline';
      style-src 'self' 'unsafe-inline'
    `
        }
    })

    // 打开开发者工具
    // mainWindow.webContents.openDevTools()

    // 监听页面加载事件
    mainWindow.webContents.on('did-start-loading', () => {
        console.log('页面开始加载')
    })

    mainWindow.webContents.on('did-stop-loading', () => {
        console.log('页面加载完成')
    })

    mainWindow.webContents.on('dom-ready', () => {
        console.log('DOM已准备就绪')
    })

    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('页面加载失败:', errorCode, errorDescription)
    })

    // // 根据环境加载不同的URL
    // if (process.env.NODE_ENV === 'development') {
    //     mainWindow.loadURL('http://localhost:5173')
    // } else {
    //     mainWindow.loadFile('dist/index.html')
    // }
    // mainWindow.loadURL('http://localhost:5173') // 开发环境（Vue CLI）
    // 先尝试加载测试页面
    const testHtmlPath = path.join(process.cwd(), 'test.html');
    console.log('尝试加载测试页面:', testHtmlPath);

    if (fs.existsSync(testHtmlPath)) {
        console.log('测试页面存在，正在加载...');
        mainWindow.loadFile(testHtmlPath);
    } else {
        console.error('测试页面不存在，尝试加载正常页面');

        // 尝试加载 HTML 文件
        // 尝试多个可能的路径
        const possiblePaths = [
            path.join(__dirname, '../dist/index.html'),
            path.join(process.cwd(), 'dist/index.html'),
            path.join(app.getAppPath(), 'dist/index.html'),
            path.join(app.getPath('exe'), '../resources/app/dist/index.html'),
            path.join(app.getPath('exe'), '../resources/app.asar/dist/index.html'),
            'dist/index.html'
        ];

        console.log('尝试加载 HTML 文件，可能的路径:', possiblePaths);

        let loaded = false;
        for (const htmlPath of possiblePaths) {
            console.log('检查路径:', htmlPath);
            if (fs.existsSync(htmlPath)) {
                console.log('HTML 文件存在，正在加载:', htmlPath);
                mainWindow.loadFile(htmlPath);
                loaded = true;
                break;
            }
        }

        if (!loaded) {
            console.error('所有路径都失败，尝试加载默认页面');
            // 如果所有路径都失败，创建一个简单的 HTML 页面
            const tempHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>加载错误</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
                    h1 { color: #e74c3c; }
                    pre { background: #f8f9fa; padding: 10px; border-radius: 5px; text-align: left; }
                </style>
            </head>
            <body>
                <h1>加载错误</h1>
                <p>无法找到主页面文件。请确保应用程序正确安装。</p>
                <p>当前工作目录: ${process.cwd()}</p>
                <p>__dirname: ${__dirname}</p>
                <p>应用路径: ${app.getAppPath()}</p>
                <p>可执行文件路径: ${app.getPath('exe')}</p>
            </body>
            </html>
            `;

            // 创建临时 HTML 文件
            const tempPath = path.join(app.getPath('temp'), 'error.html');
            fs.writeFileSync(tempPath, tempHtml);
            mainWindow.loadFile(tempPath);
        }
    }

    // 创建缓存目录
    if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true })
    }

    // 窗口加载完成后加载上次保存的图片
    mainWindow.webContents.on('did-finish-load', () => {
        console.log('窗口加载完成，尝试加载上次保存的图片...');

        // 从文件加载上次保存的图片路径
        const savedData = loadImagePathsFromFile()
        console.log('从文件加载的数据:', savedData);

        if (savedData && (savedData.imagePath1 || savedData.imagePath2)) {
            console.log('从文件加载上次保存的图片路径:', savedData)

            // 验证文件是否存在
            let imagePath1Exists = false;
            let imagePath2Exists = false;

            if (savedData.imagePath1) {
                try {
                    imagePath1Exists = fs.existsSync(savedData.imagePath1);
                    console.log(`第一张图片文件是否存在: ${imagePath1Exists}`);
                } catch (error) {
                    console.error('检查第一张图片文件是否存在时出错:', error);
                }
            }

            if (savedData.imagePath2) {
                try {
                    imagePath2Exists = fs.existsSync(savedData.imagePath2);
                    console.log(`第二张图片文件是否存在: ${imagePath2Exists}`);
                } catch (error) {
                    console.error('检查第二张图片文件是否存在时出错:', error);
                }
            }

            // 将路径保存到全局变量
            if (savedData.imagePath1 && imagePath1Exists) {
                currentImagePath1 = savedData.imagePath1 || ''
                currentTimeStep1 = savedData.timeStep1 || ''
            }

            if (savedData.imagePath2 && imagePath2Exists) {
                currentImagePath2 = savedData.imagePath2 || ''
                currentTimeStep2 = savedData.timeStep2 || ''
            }

            // 将路径发送给渲染进程
            const dataToSend = {
                imagePath1: imagePath1Exists ? savedData.imagePath1 : '',
                imagePath2: imagePath2Exists ? savedData.imagePath2 : '',
                timeStep1: imagePath1Exists ? savedData.timeStep1 : '',
                timeStep2: imagePath2Exists ? savedData.timeStep2 : ''
            };

            console.log('发送给渲染进程的数据:', dataToSend);
            mainWindow.webContents.send('load-saved-images', dataToSend);

            // 延迟 1 秒再次发送，确保渲染进程已经准备好接收
            setTimeout(() => {
                console.log('再次发送数据给渲染进程:', dataToSend);
                mainWindow.webContents.send('load-saved-images', dataToSend);
            }, 1000);
        } else {
            console.log('没有找到保存的图片路径');
        }
    })

    // 窗口关闭前保存当前图片路径
    mainWindow.on('close', () => {
        if (currentImagePath1 || currentImagePath2) {
            console.log('窗口关闭前保存当前图片路径:', currentImagePath1, currentImagePath2)
            saveImagePathsToFile(currentImagePath1, currentImagePath2, currentTimeStep1, currentTimeStep2)
        }
    })
}

//后端处理
// 启动后端服务
let backendProcess = null;

function startBackend() {
    console.log(`启动后端服务: ${serverPath}`);

    backendProcess = spawn('node', [serverPath], {
        cwd: path.dirname(serverPath),
        stdio: 'inherit'
    });

    backendProcess.on('error', (err) => {
        console.error('启动后端服务失败:', err);
    });

    backendProcess.on('exit', (code, signal) => {
        console.log(`后端服务退出，代码: ${code}, 信号: ${signal}`);
    });
}

// 处理获取用户名的IPC请求
ipcMain.handle('get-username', () => {
    try {
        // 获取系统用户名
        const username = os.userInfo().username;
        return username;
    } catch (error) {
        console.error('获取用户名失败:', error);
        return '未知用户';
    }
});

// 开始监听指定目录的图片文件
function startWatchingDirectory(directoryPath, mainWindow) {
    try {
        // 如果已经有该目录的监听器，先关闭
        if (watchers[directoryPath]) {
            watchers[directoryPath].close();
        }

        // 创建目录（如果不存在）
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
            console.log(`创建目录: ${directoryPath}`);
        }

        // 初始化监听器
        watchers[directoryPath] = chokidar.watch(directoryPath, {
            ignored: /(^|\/)\.[^\/\.]/, // 忽略隐藏文件
            persistent: true,
            ignoreInitial: false, // 初始扫描时触发事件
            awaitWriteFinish: {
                stabilityThreshold: 2000, // 文件写入稳定后等待2秒
                pollInterval: 100 // 每100毫秒检查一次
            }
        });

        // 监听新增文件
        watchers[directoryPath].on('add', filePath => {
            const ext = path.extname(filePath).toLowerCase();
            // 只处理TIFF文件
            if (ext === '.tiff' || ext === '.tif') {
                console.log(`发现新图片: ${filePath}`);
                // 更新最新图片路径
                latestImagePaths[directoryPath] = filePath;
                // 通知渲染进程有新图片
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.webContents.send('new-image-detected', filePath);
                }
            }
        });

        // 监听文件变化
        watchers[directoryPath].on('change', filePath => {
            const ext = path.extname(filePath).toLowerCase();
            // 只处理TIFF文件
            if (ext === '.tiff' || ext === '.tif') {
                console.log(`图片已更新: ${filePath}`);
                // 更新最新图片路径
                latestImagePaths[directoryPath] = filePath;
                // 通知渲染进程有更新的图片
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.webContents.send('new-image-detected', filePath);
                }
            }
        });

        // 监听文件删除
        watchers[directoryPath].on('unlink', filePath => {
            const ext = path.extname(filePath).toLowerCase();
            // 只处理TIFF文件
            if (ext === '.tiff' || ext === '.tif') {
                console.log(`图片已删除: ${filePath}`);
                // 通知前端文件已删除
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.webContents.send('image-deleted', filePath);
                }
            }
        });

        console.log(`开始监听目录: ${directoryPath}`);
        return true;
    } catch (error) {
        console.error('监听目录时出错:', error);
        return false;
    }
}

// 获取指定目录中的最新TIFF文件
async function getLatestTiffFile(directoryPath) {
    try {
        console.log(`🔍 getLatestTiffFile 被调用，目录: ${directoryPath}`);

        // 检查目录是否存在
        if (!fs.existsSync(directoryPath)) {
            console.error(`❌ 目录不存在: ${directoryPath}`);
            return null;
        }

        // 读取目录中的所有文件
        const files = fs.readdirSync(directoryPath);
        console.log(`📁 目录中的所有文件: ${files.join(', ')}`);

        // 过滤出TIFF文件并按修改时间排序
        const tiffFiles = [];
        for (const file of files) {
            const filePath = path.join(directoryPath, file);
            const ext = path.extname(file).toLowerCase();

            // 使用正则表达式匹配文件名模式
            const pattern = /final_predicted_results_.*\.tiff?/i;
            const matches = pattern.test(file);

            console.log(`📋 检查文件: ${file}, 扩展名: ${ext}, 匹配模式: ${matches}`);

            if ((ext === '.tiff' || ext === '.tif') && matches) {
                const stats = fs.statSync(filePath);
                tiffFiles.push({
                    path: filePath,
                    mtime: stats.mtime.getTime()
                });
                console.log(`✅ 找到匹配的TIFF文件: ${file}`);
            }
        }

        // 按修改时间降序排序（最新的在前）
        tiffFiles.sort((a, b) => b.mtime - a.mtime);
        console.log(`📊 找到 ${tiffFiles.length} 个匹配的TIFF文件`);

        // 返回最新的文件路径
        if (tiffFiles.length > 0) {
            console.log(`✅ 找到最新的TIFF文件: ${tiffFiles[0].path}`);
            return tiffFiles[0].path;
        } else {
            console.log(`❌ 目录中没有匹配的TIFF文件: ${directoryPath}`);
            return null;
        }
    } catch (error) {
        console.error('获取最新TIFF文件时出错:', error);
        return null;
    }
}

// 处理开始监听目录的IPC请求
ipcMain.handle('start-watching-directory', async (_, directoryPath) => {
    return startWatchingDirectory(directoryPath, BrowserWindow.getFocusedWindow());
});

// 处理获取最新TIFF文件的IPC请求
ipcMain.handle('get-latest-tiff-file', async (_, directoryPath) => {
    return await getLatestTiffFile(directoryPath);
});

// 处理获取当前缓存的最新图片路径的IPC请求
ipcMain.handle('get-cached-latest-image', (_, directoryPath) => {
    return directoryPath ? latestImagePaths[directoryPath] : null;
});

// 注意：清除图片缓存的IPC处理程序已移至下方

// 处理检查文件是否存在的IPC请求
ipcMain.handle('file-exists', async (_, filePath) => {
    try {
        // 验证路径安全性
        if (!isValidLocalPath(filePath)) {
            throw new Error('Invalid file path');
        }

        // 检查文件是否存在
        return fs.existsSync(filePath);
    } catch (error) {
        console.error('Error checking if file exists:', error);
        return false;
    }
});

// 获取指定文件夹中最新的图片文件
async function getLatestImageFile(folderPath, pattern = 'final_predicted_results_*.0s.tiff') {
    try {
        console.log(`🔍 getLatestImageFile 被调用，目录: ${folderPath}, 模式: ${pattern}`);

        // 验证路径安全性
        if (!isValidLocalPath(folderPath)) {
            throw new Error('Invalid folder path');
        }

        // 检查文件夹是否存在
        if (!fs.existsSync(folderPath)) {
            console.error(`❌ 文件夹不存在: ${folderPath}`);
            throw new Error(`Folder does not exist: ${folderPath}`);
        }

        // 读取文件夹中的所有文件
        const files = fs.readdirSync(folderPath);
        console.log(`📁 文件夹中的所有文件: ${files.join(', ')}`);

        // 过滤出符合模式的文件
        const patternRegex = new RegExp(pattern.replace('*', '.*'));
        console.log(`🔍 使用正则表达式: ${patternRegex}`);

        const matchingFiles = files.filter(file => {
            const matches = patternRegex.test(file);
            console.log(`📋 检查文件: ${file}, 匹配: ${matches}`);
            return matches;
        });

        console.log(`📊 找到 ${matchingFiles.length} 个匹配的文件: ${matchingFiles.join(', ')}`);

        if (matchingFiles.length === 0) {
            console.log(`❌ 没有找到匹配的文件`);
            return null;
        }

        // 按文件修改时间排序，获取最新的文件
        const fileStats = matchingFiles.map(file => {
            const filePath = path.join(folderPath, file);
            return {
                name: file,
                path: filePath,
                mtime: fs.statSync(filePath).mtime
            };
        });

        // 按修改时间降序排序
        fileStats.sort((a, b) => b.mtime - a.mtime);

        // 返回最新的文件路径
        console.log(`✅ 找到最新的图片文件: ${fileStats[0].path}`);
        return fileStats[0].path;
    } catch (error) {
        console.error('Error getting latest image file:', error);
        return null;
    }
}

// 处理获取最新图片文件的IPC请求
ipcMain.handle('get-latest-image-file', async (_, folderPath, pattern) => {
    try {
        const latestFilePath = await getLatestImageFile(folderPath, pattern);
        return latestFilePath;
    } catch (error) {
        console.error('Error handling get-latest-image-file:', error);
        return null;
    }
});

// 处理获取图片 data URL 的IPC请求
ipcMain.handle('get-image-data-url', async (_, imagePath) => {
    try {
        // 验证路径安全性
        if (!isValidLocalPath(imagePath)) {
            throw new Error('Invalid image path');
        }

        // 获取图片的 data URL
        return await getImageAsDataUrl(imagePath);
    } catch (error) {
        console.error('Error getting image data URL:', error);
        return null;
    }
});

// 处理将 TIFF 转换为 JPEG 并返回 data URL 的IPC请求
ipcMain.handle('get-local-image-as-jpeg', async (_, imagePath) => {
    try {
        // 验证路径安全性
        if (!isValidLocalPath(imagePath)) {
            throw new Error('Invalid image path');
        }

        // 获取文件扩展名
        const ext = path.extname(imagePath).toLowerCase();

        // 如果是 TIFF 格式，转换为 JPEG
        if (ext === '.tiff' || ext === '.tif') {
            return await convertTiffToJpegDataUrl(imagePath);
        }

        // 如果已经是 JPEG 格式，直接返回 data URL
        if (ext === '.jpg' || ext === '.jpeg') {
            return await getImageAsDataUrl(imagePath);
        }

        // 如果是 PNG 格式，转换为 JPEG
        if (ext === '.png') {
            // 生成哈希作为缓存文件名
            const hash = crypto.createHash('md5').update(imagePath).digest('hex');
            const jpegPath = path.join(cacheDir, `${hash}.jpg`);

            // 转换 PNG 为 JPEG
            await sharp(imagePath)
                .jpeg({ quality: 90 })
                .toFile(jpegPath);

            return await getImageAsDataUrl(jpegPath);
        }

        // 其他格式直接返回 null
        return null;
    } catch (error) {
        console.error('Error converting to JPEG:', error);
        return null;
    }
});

// 处理保存图片路径的IPC请求
ipcMain.handle('save-image-paths', async (_, imagePath1, imagePath2, timeStep1, timeStep2) => {
    // 保存到全局变量
    currentImagePath1 = imagePath1 || ''
    currentImagePath2 = imagePath2 || ''
    currentTimeStep1 = timeStep1 || ''
    currentTimeStep2 = timeStep2 || ''

    console.log(`保存图片路径到全局变量: ${currentImagePath1}, ${currentImagePath2}`)

    // 同时保存到文件
    return saveImagePathsToFile(imagePath1, imagePath2, timeStep1, timeStep2)
})

// 处理加载图片路径的IPC请求
ipcMain.handle('load-image-paths', async () => {
    // 先尝试从全局变量加载
    if (currentImagePath1 || currentImagePath2) {
        console.log(`从全局变量加载图片路径: ${currentImagePath1}, ${currentImagePath2}`)
        return {
            imagePath1: currentImagePath1,
            imagePath2: currentImagePath2,
            timeStep1: currentTimeStep1,
            timeStep2: currentTimeStep2
        }
    }

    // 如果全局变量中没有数据，尝试从文件加载
    const data = loadImagePathsFromFile()
    if (data) {
        // 如果成功从文件加载，保存到全局变量
        currentImagePath1 = data.imagePath1 || ''
        currentImagePath2 = data.imagePath2 || ''
        currentTimeStep1 = data.timeStep1 || ''
        currentTimeStep2 = data.timeStep2 || ''
        console.log(`从文件加载图片路径并保存到全局变量: ${currentImagePath1}, ${currentImagePath2}`)
    }

    return data
})

// 处理获取目录图片的IPC请求
ipcMain.handle('get-directory-images', async (_, directoryPath, extensions = ['tiff', 'jpg', 'jpeg', 'png']) => {
    try {
        console.log(`获取目录图片: ${directoryPath}`)

        // 验证路径安全性
        if (!isValidLocalPath(directoryPath)) {
            throw new Error('Invalid directory path')
        }

        // 检查目录是否存在
        if (!fs.existsSync(directoryPath)) {
            return {
                success: false,
                error: '目录不存在',
                images: []
            }
        }

        // 检查是否为目录
        const stats = fs.statSync(directoryPath)
        if (!stats.isDirectory()) {
            return {
                success: false,
                error: '路径不是一个目录',
                images: []
            }
        }

        // 读取目录内容
        const files = fs.readdirSync(directoryPath)
        const images = []

        // 过滤图片文件
        for (const file of files) {
            const filePath = path.join(directoryPath, file)
            const fileStats = fs.statSync(filePath)

            if (fileStats.isFile()) {
                const ext = path.extname(file).toLowerCase().slice(1)
                if (extensions.includes(ext)) {
                    images.push({
                        name: file,
                        path: filePath,
                        size: fileStats.size,
                        modified: fileStats.mtime,
                        extension: ext
                    })
                }
            }
        }

        // 按修改时间排序（最新的在前）
        images.sort((a, b) => b.modified - a.modified)

        console.log(`找到 ${images.length} 个图片文件`)
        return {
            success: true,
            images: images,
            count: images.length
        }

    } catch (error) {
        console.error('获取目录图片失败:', error)
        return {
            success: false,
            error: error.message,
            images: []
        }
    }
})

// 处理清除图片缓存的IPC请求
ipcMain.handle('clear-image-cache', async (_, imagePath) => {
    try {
        console.log(`尝试清除图片缓存: ${imagePath}`)

        // 生成哈希作为缓存文件名
        const hash = crypto.createHash('md5').update(imagePath).digest('hex')
        const jpegPath = path.join(cacheDir, `${hash}.jpg`)

        // 如果缓存文件存在，删除它
        if (fs.existsSync(jpegPath)) {
            fs.unlinkSync(jpegPath)
            console.log(`删除缓存文件: ${jpegPath}`)
            return true
        }

        console.log(`缓存文件不存在: ${jpegPath}`)
        return false
    } catch (error) {
        console.error('清除图片缓存失败:', error)
        return false
    }
})

app.on('ready', () => {
    console.log('应用程序已准备就绪')
    console.log('当前工作目录:', process.cwd())
    console.log('__dirname:', __dirname)
    console.log('用户数据路径:', userDataPath)
    // 检查dist目录是否存在
    const distPath = path.join(process.cwd(), 'dist')
    console.log('检查dist目录:', distPath, fs.existsSync(distPath))
    if (fs.existsSync(distPath)) {
        console.log('dist目录内容:', fs.readdirSync(distPath))
    }

    // 注册协议
    registerLocalFileProtocol()
    //启动后端服务
    startBackend();

    // 创建窗口
    createWindow()

    //兼容核心代码 1----windows和lunx
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })
})

// Windows/Linux的窗口关闭行为
app.on('window-all-closed', () => {
    // 在应用程序关闭前保存图片路径
    if (currentImagePath1 || currentImagePath2) {
        console.log('应用程序即将关闭，保存当前图片路径...')
        saveImagePathsToFile(currentImagePath1, currentImagePath2, currentTimeStep1, currentTimeStep2)
    }

    if (process.platform !== 'darwin') app.quit()
})

// 在应用程序退出前保存图片路径
app.on('will-quit', () => {
    // 在应用程序退出前保存图片路径
    if (currentImagePath1 || currentImagePath2) {
        console.log('应用程序即将退出，保存当前图片路径...')
        saveImagePathsToFile(currentImagePath1, currentImagePath2, currentTimeStep1, currentTimeStep2)
    }
    // 关闭后端服务
    if (backendProcess) {
        console.log('关闭后端服务...');
        backendProcess.kill('SIGINT');
    }
})
