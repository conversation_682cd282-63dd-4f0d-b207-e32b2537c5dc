<template>
  <div class="image-display">
    <div class="image-grid">
      <div v-for="(image, index) in images" :key="index" class="image-card"
        :class="{ 'active': selectedImageIndex === index }" @click="selectImage(index)">
        <!-- <div class="refresh-button" @click.stop="refreshImage(index)" title="刷新图片">
          <span>刷新</span>
        </div> -->
        <div v-if="isLoading && index === 0" class="loading-overlay">
          <span>Loading...</span>
        </div>
        <!-- <div v-else-if="loadError && index === 0" class="error-overlay">
          <span>Failed to load image</span>
        </div> -->
        <img :src="image.url" :alt="image.title" @error="handleImageError(index)" />
        <div class="image-overlay"></div>
        <div class="image-title">{{ image.title }}</div>
      </div>
    </div>

    <div class="image-description">
      <h3>{{ selectedImage.title }}</h3>
      <p>{{ selectedImage.description }}</p>
      <!-- <div v-if="debugInfo" class="debug-info">
        <h4>Debug Info:</h4>
        <pre>{{ debugInfo }}</pre>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useThicknessStore } from '../stores/thicknessStore'

// 使用store
const thicknessStore = useThicknessStore()

// 当前用户数据
const username = ref('')

// 图片加载状态
const isLoading = ref(true)
const loadError = ref(false)

// 第一张图片路径
const localImagePath = computed(() => {
  // 如果有最新发现的路径，使用它；否则使用默认路径
  return latestDiscoveredImagePath.value || `C:/Users/<USER>/AppData/Local/Temp/YFL/mcrCache9.14/run_pr0/run_predicti/Predicted_Results/final_predicted_results_1.0s.tiff`
})

// 第一张图片目录路径
const imageDirectoryPath = computed(() => {
  return `C:/Users/<USER>/AppData/Local/Temp/YFL/mcrCache9.14/run_pr0/run_predicti/Predicted_Results/`
})

// 第二张图片路径
const localImagePath2 = computed(() => {
  // 如果有最新发现的路径，使用它；否则使用默认路径
  return latestDiscoveredImagePath2.value || `C:\\Program Files\\eblow_prediction_version\\application\\untitled.tif`
})

// 第二张图片目录路径
const imageDirectoryPath2 = computed(() => {
  return `C:\\Program Files\\eblow_prediction_version\\application\\`
})

// 存储第一张图片最新发现的路径
const latestDiscoveredImagePath = ref('')

// 存储第二张图片最新发现的路径
const latestDiscoveredImagePath2 = ref('')

// 存储第一张图片提取的时间步数值
const timeStepValue = ref('2')

// 存储第二张图片提取的时间步数值
const timeStepValue2 = ref('2')

// 存储第一张图片当前时间步数
const currentTimeStep = ref('*')

// 存储第二张图片当前时间步数
const currentTimeStep2 = ref('*')

// 上次保存的图片路径，用于避免重复保存
const lastSavedImagePath1 = ref('')
const lastSavedImagePath2 = ref('')

// 保存当前状态的函数
const saveCurrentState = async () => {
  console.log('保存当前图片路径...')

  try {
    // 输出当前状态
    console.log(`当前第一张图片路径: ${latestDiscoveredImagePath.value || '无'}`)
    console.log(`当前第二张图片路径: ${latestDiscoveredImagePath2.value || '无'}`)
    console.log(`当前第一张图片时间步数: ${currentTimeStep.value || '无'}`)
    console.log(`当前第二张图片时间步数: ${currentTimeStep2.value || '无'}`)

    // 准备要保存的路径
    let imagePath1 = ''
    let imagePath2 = ''

    // 保存当前显示的图片路径
    if (latestDiscoveredImagePath.value) {
      imagePath1 = latestDiscoveredImagePath.value
      console.log(`准备保存第一张图片路径: ${imagePath1}`)
    } else if (images.value[0].url && images.value[0].url.startsWith('data:')) {
      // 如果没有路径但有URL，保存默认路径
      imagePath1 = localImagePath.value
      console.log(`准备保存第一张图片默认路径: ${imagePath1}`)
    }

    if (latestDiscoveredImagePath2.value) {
      imagePath2 = latestDiscoveredImagePath2.value
      console.log(`准备保存第二张图片路径: ${imagePath2}`)
    } else if (images.value[1].url && images.value[1].url.startsWith('data:')) {
      // 如果没有路径但有URL，保存默认路径
      imagePath2 = localImagePath2.value
      console.log(`准备保存第二张图片默认路径: ${imagePath2}`)
    }

    // 检查是否与上次保存的路径相同，如果相同则不重复保存
    if (imagePath1 === lastSavedImagePath1.value && imagePath2 === lastSavedImagePath2.value) {
      console.log('图片路径未变化，跳过保存')
      return
    }

    // 验证路径是否存在
    if (imagePath1 && window.electronAPI) {
      const fileExists = await window.electronAPI.fileExists(imagePath1)
      console.log(`第一张图片文件是否存在: ${fileExists}`)
    }

    if (imagePath2 && window.electronAPI) {
      const fileExists = await window.electronAPI.fileExists(imagePath2)
      console.log(`第二张图片文件是否存在: ${fileExists}`)
    }

    // 保存到 Electron 主进程
    if (window.electronAPI && window.electronAPI.saveImagePaths) {
      console.log('使用 Electron API 保存图片路径')
      console.log(`保存的路径: ${imagePath1}, ${imagePath2}`)
      console.log(`保存的时间步数: ${currentTimeStep.value}, ${currentTimeStep2.value}`)

      const result = await window.electronAPI.saveImagePaths(
        imagePath1,
        imagePath2,
        currentTimeStep.value,
        currentTimeStep2.value
      )
      console.log(`保存结果: ${result ? '成功' : '失败'}`)

      // 更新上次保存的路径
      lastSavedImagePath1.value = imagePath1
      lastSavedImagePath2.value = imagePath2
    } else {
      // 如果没有 Electron API，回退到使用 localStorage
      console.log('使用 localStorage 保存图片路径')
      localStorage.setItem('last-image-path', imagePath1)
      localStorage.setItem('last-image-path-2', imagePath2)
      localStorage.setItem('last-time-step', currentTimeStep.value)
      localStorage.setItem('last-time-step-2', currentTimeStep2.value)

      // 更新上次保存的路径
      lastSavedImagePath1.value = imagePath1
      lastSavedImagePath2.value = imagePath2
    }

    // 输出当前保存的路径信息，用于调试
    console.log(`当前 localStorage 中的第一张图片路径: ${localStorage.getItem('last-image-path')}`)
    console.log(`当前 localStorage 中的第二张图片路径: ${localStorage.getItem('last-image-path-2')}`)
  } catch (error) {
    console.error(`保存状态时出错: ${error}`)
  }
}

// 从文件名中提取时间步数
function extractTimeStep(filePath) {
  try {
    if (!filePath) return null

    // 获取文件名
    const fileName = filePath.split('/').pop().split('\\').pop()

    // 使用正则表达式提取时间步数值
    const regex = /final_predicted_results_([\d.]+)\.0s\.tiff?/i
    const match = fileName.match(regex)

    if (match && match[1]) {
      console.log(`提取到时间步数: ${match[1]}`)
      return match[1]
    }

    return null
  } catch (error) {
    console.error('提取时间步数时出错:', error)
    return null
  }
}

// 更新第一张图片标题
function updateImageTitle(timeStep) {
  // 不需要手动更新标题，因为标题是一个计算属性
  // 当 currentTimeStep.value 更新时，标题会自动更新
  console.log(`更新第一张图片标题为: 预测${timeStep}时间步数的分析结果`)
}

// 更新第二张图片标题
function updateImageTitle2(timeStep) {
  // 不需要手动更新标题，因为标题是一个计算属性
  // 当 currentTimeStep2.value 更新时，标题会自动更新
  console.log(`更新第二张图片标题为: 预测${timeStep}时间步数的分析结果`)
}



// 示例图片数据
const images = ref([
  {
    url: '../src/assets/1.tiff',  // 默认占位图
    title: computed(() => `预测${currentTimeStep.value}秒时间步数的分析结果`),
    description: '此图展示了预测时间步数的分析结果，随着时间的推进表示不同时间测试的变化数据。'
  },
  {
    url: '../src/assets/1.tiff',
    title: computed(() => `组合功能模块分析结果`),
    description: '此图表示弯头气固冲蚀预测及未来状态演化。(详情可查看文件库)'
  }
])

// 调试信息
const debugInfo = ref('')

// 尝试使用 data URL 加载图片
const tryLoadWithDataUrl = async (imagePath) => {
  try {
    if (!window.electronAPI) return false

    debugInfo.value += '\n尝试使用 data URL 加载图片...'

    // 尝试获取图片的 data URL
    const dataUrl = await window.electronAPI.getImageDataUrl?.(imagePath)

    if (dataUrl) {
      debugInfo.value += '\n成功获取 data URL'
      images.value[0].url = dataUrl
      return true
    } else {
      debugInfo.value += '\n无法获取 data URL'
      return false
    }
  } catch (error) {
    debugInfo.value += `\n获取 data URL 时出错: ${error.message}`
    return false
  }
}

// 尝试使用其他格式加载图片
const tryAlternativeFormats = async () => {
  try {
    if (!window.electronAPI) return false

    debugInfo.value += '\n尝试使用其他格式加载图片...'

    // 尝试获取 JPEG 格式
    const jpegUrl = await window.electronAPI.getLocalImageAsJpeg?.(localImagePath.value)

    if (jpegUrl) {
      debugInfo.value += '\n成功获取 JPEG 格式图片'
      images.value[0].url = jpegUrl
      return true
    } else {
      debugInfo.value += '\n无法获取 JPEG 格式图片'
      return false
    }
  } catch (error) {
    debugInfo.value += `\n获取其他格式图片时出错: ${error.message}`
    return false
  }
}

// 处理图片加载错误
const handleImageError = async (index) => {
  console.error(`图片 ${index} 加载失败`)
  loadError.value = true
  debugInfo.value += `\n图片 ${index} 加载失败: ${images.value[index].url}`

  // 如果是第一张图片，尝试其他方法
  if (index === 0) {
    // 尝试使用 data URL
    if (await tryLoadWithDataUrl(localImagePath.value)) {
      return
    }

    // 尝试其他格式
    if (await tryAlternativeFormats()) {
      return
    }

    // 如果所有方法都失败，使用占位图
    images.value[0].url = 'https://via.placeholder.com/800x400/ff0000/ffffff?text=加载失败'
  }
}

// 在组件挂载时加载本地图片
onMounted(async () => {
  console.log('组件挂载，开始注册事件监听器...');

  // 添加事件监听器
  window.addEventListener('beforeunload', saveCurrentState)

  // 注册加载保存的图片事件监听器
  if (window.electronAPI && window.electronAPI.onLoadSavedImages) {
    console.log('注册 onLoadSavedImages 事件监听器');
    window.electronAPI.onLoadSavedImages(async (data) => {
      console.log('接收到保存的图片路径:', data)

      // 加载第一张图片
      if (data.imagePath1) {
        console.log(`尝试加载第一张图片: ${data.imagePath1}`);
        const fileExists = await window.electronAPI.fileExists(data.imagePath1)
        console.log(`第一张图片文件是否存在: ${fileExists}`);

        if (fileExists) {
          console.log(`开始加载第一张图片: ${data.imagePath1}`)
          await loadImage(data.imagePath1)
          latestDiscoveredImagePath.value = data.imagePath1
          if (data.timeStep1) {
            currentTimeStep.value = data.timeStep1
          }
        }
      }

      // 加载第二张图片
      if (data.imagePath2) {
        console.log(`尝试加载第二张图片: ${data.imagePath2}`);
        const fileExists = await window.electronAPI.fileExists(data.imagePath2)
        console.log(`第二张图片文件是否存在: ${fileExists}`);

        if (fileExists) {
          console.log(`开始加载第二张图片: ${data.imagePath2}`)
          await loadImage2(data.imagePath2)
          latestDiscoveredImagePath2.value = data.imagePath2
          if (data.timeStep2) {
            currentTimeStep2.value = data.timeStep2
          }
        }
      }
    })
  } else {
    console.warn('electronAPI.onLoadSavedImages 不可用，无法注册事件监听器');
  }
  try {
    // 检查是否在Electron环境中
    if (window.electronAPI) {
      // 获取用户名
      username.value = await window.electronAPI.getUsername()
      console.log(`当前用户: ${username.value}`)

      // 输出当前保存的路径信息，用于调试
      console.log('===== 持久化数据调试信息 =====');
      console.log(`localStorage 中的第一张图片路径: ${localStorage.getItem('last-image-path')}`);
      console.log(`localStorage 中的第一张图片时间步数: ${localStorage.getItem('last-time-step')}`);
      console.log(`localStorage 中的第二张图片路径: ${localStorage.getItem('last-image-path-2')}`);
      console.log(`localStorage 中的第二张图片时间步数: ${localStorage.getItem('last-time-step-2')}`);
      console.log(`Pinia store 中的第一张图片路径: ${thicknessStore.lastImagePath}`);
      console.log(`Pinia store 中的第二张图片路径: ${thicknessStore.lastImagePath2}`);
      console.log('===== 持久化数据调试信息结束 =====');

      // 尝试从 Electron 主进程加载图片路径
      let savedPaths = null
      if (window.electronAPI.loadImagePaths) {
        console.log('尝试使用 Electron API 加载图片路径')
        savedPaths = await window.electronAPI.loadImagePaths()
        console.log('从 Electron 主进程加载的路径:', savedPaths)
      }

      // 如果成功从 Electron 主进程加载了路径，使用这些路径
      let lastImagePath = null
      let lastImagePath2 = null
      let lastTimeStep = null
      let lastTimeStep2 = null

      if (savedPaths) {
        lastImagePath = savedPaths.imagePath1
        lastImagePath2 = savedPaths.imagePath2
        lastTimeStep = savedPaths.timeStep1
        lastTimeStep2 = savedPaths.timeStep2
        console.log(`从 Electron 主进程加载的第一张图片路径: ${lastImagePath}`)
        console.log(`从 Electron 主进程加载的第二张图片路径: ${lastImagePath2}`)
      } else {
        // 如果从 Electron 主进程加载失败，尝试从 localStorage 中获取
        console.log('从 localStorage 中获取图片路径')
        lastImagePath = localStorage.getItem('last-image-path')
        lastImagePath2 = localStorage.getItem('last-image-path-2')
        lastTimeStep = localStorage.getItem('last-time-step')
        lastTimeStep2 = localStorage.getItem('last-time-step-2')
      }

      // 加载第一张图片
      if (lastImagePath) {
        console.log(`尝试加载上次打开的第一张图片: ${lastImagePath}`)
        const fileExists = await window.electronAPI.fileExists(lastImagePath)
        console.log(`第一张图片文件是否存在: ${fileExists}`)

        if (fileExists) {
          // 如果文件存在，加载它
          console.log(`开始加载第一张图片: ${lastImagePath}`)
          await loadImage(lastImagePath)
          // 保存路径到全局变量，便于后续保存
          latestDiscoveredImagePath.value = lastImagePath

          // 设置时间步数
          if (lastTimeStep) {
            currentTimeStep.value = lastTimeStep
          }
        } else {
          // 如果文件不存在，尝试获取并加载最新的图片
          console.log('上次打开的第一张图片不存在，尝试加载最新图片')
          await fetchAndLoadLatestImage()
        }
      } else {
        // 如果没有上次打开的图片路径，尝试获取并加载最新的图片
        console.log('没有上次打开的第一张图片路径，尝试获取并加载最新图片')
        await fetchAndLoadLatestImage()
      }

      // 加载第二张图片 - 总是尝试获取最新的图片
      console.log('第二张图片：尝试获取并加载最新图片')
      await fetchAndLoadLatestImage2()

      // 如果没有找到最新图片，则尝试使用上次保存的路径
      if (!latestDiscoveredImagePath2.value && lastImagePath2) {
        console.log(`第二张图片：没有找到最新图片，尝试加载上次打开的图片: ${lastImagePath2}`)
        const fileExists = await window.electronAPI.fileExists(lastImagePath2)
        console.log(`第二张图片文件是否存在: ${fileExists}`)

        if (fileExists) {
          console.log(`开始加载第二张图片: ${lastImagePath2}`)
          await loadImage2(lastImagePath2)
          latestDiscoveredImagePath2.value = lastImagePath2

          // 设置时间步数
          if (lastTimeStep2) {
            currentTimeStep2.value = lastTimeStep2
          }
        }
      }

      // 立即保存当前状态，确保下次打开时能加载正确的图片
      await saveCurrentState()

      // 初始化上次保存的路径
      lastSavedImagePath1.value = latestDiscoveredImagePath.value || ''
      lastSavedImagePath2.value = latestDiscoveredImagePath2.value || ''

      // 开始监听图片目录
      await startWatchingImageDirectory()
    } else {
      console.log('非Electron环境，使用默认图片')
    }
  } catch (error) {
    console.error('初始化时出错:', error)
    loadError.value = true
  } finally {
    isLoading.value = false
  }
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  console.log('组件卸载，移除事件监听器...');

  if (window.electronAPI) {
    if (window.electronAPI.removeNewImageListener) {
      console.log('移除 newImageListener 事件监听器');
      window.electronAPI.removeNewImageListener()
    }

    // 移除加载保存的图片事件监听器
    if (window.electronAPI.removeLoadSavedImagesListener) {
      console.log('移除 loadSavedImagesListener 事件监听器');
      window.electronAPI.removeLoadSavedImagesListener()
    }
  }

  // 移除 beforeunload 事件监听器
  console.log('移除 beforeunload 事件监听器');
  window.removeEventListener('beforeunload', saveCurrentState)

  // 不再需要清除定时器，因为我们不再使用定时器

  // 在卸载前保存当前状态
  console.log('在卸载前保存当前状态');
  saveCurrentState();
})

// 选中的图片索引
const selectedImageIndex = ref(0)

// 选中的图片
const selectedImage = computed(() => images.value[selectedImageIndex.value])

// 选中图片的方法
const selectImage = (index) => {
  selectedImageIndex.value = index
}

// // 刷新图片的方法
// async function refreshImage(index) {
//   console.log(`刷新图片: ${index}`)

//   try {
//     if (index === 0) {
//       // 刷新第一张图片
//       await fetchAndLoadLatestImage()
//     } else if (index === 1) {
//       // 刷新第二张图片
//       await fetchAndLoadLatestImage2()
//     }

//     // 刷新后立即保存当前状态
//     await saveCurrentState()
//   } catch (error) {
//     console.error(`刷新图片时出错: ${error}`)
//   }
// }

// 加载图片的方法
async function loadImage(imagePath) {
  try {
    isLoading.value = true
    loadError.value = false
    debugInfo.value = `开始加载图片: ${imagePath}`

    // 提取时间步数并更新标题
    const extractedTimeStep = extractTimeStep(imagePath)
    if (extractedTimeStep) {
      timeStepValue.value = extractedTimeStep
      currentTimeStep.value = extractedTimeStep
      updateImageTitle(extractedTimeStep)

      // 保存最后打开的图片路径和时间步数
      console.log(`保存第一张图片路径到 Pinia store: ${imagePath}`)
      console.log(`保存第一张图片时间步数到 Pinia store: ${extractedTimeStep}`)
      thicknessStore.saveLastImagePath(imagePath)
      thicknessStore.saveLastTimeStep(extractedTimeStep)

      // 验证保存是否成功
      console.log(`保存后的 localStorage 值: ${localStorage.getItem('last-image-path')}`)

      // 保存到 Electron 主进程
      if (window.electronAPI && window.electronAPI.saveImagePaths) {
        await window.electronAPI.saveImagePaths(
          imagePath,
          latestDiscoveredImagePath2.value,
          extractedTimeStep,
          currentTimeStep2.value
        )
        console.log('已将第一张图片路径保存到 Electron 主进程')
      }
    }

    // 检查是否在Electron环境中
    if (window.electronAPI) {
      // 检查文件是否存在
      const fileExists = await window.electronAPI.fileExists(imagePath)
      debugInfo.value += `\n文件存在: ${fileExists}`

      if (fileExists) {
        // 直接尝试使用 JPEG 格式
        debugInfo.value += '\n尝试使用 JPEG 格式加载图片...'
        const jpegDataUrl = await window.electronAPI.getLocalImageAsJpeg(imagePath)

        // 直接保存路径
        latestDiscoveredImagePath.value = imagePath
        // 立即保存当前状态
        await saveCurrentState()

        if (jpegDataUrl) {
          debugInfo.value += '\n成功获取 JPEG 格式图片'
          images.value[0].url = jpegDataUrl
          console.log('成功加载 JPEG 格式图片')
          return true
        }

        // 如果 JPEG 失败，尝试使用普通的 data URL
        debugInfo.value += '\n尝试使用普通 data URL...'
        const dataUrl = await window.electronAPI.getImageDataUrl(imagePath)

        if (dataUrl) {
          debugInfo.value += '\n成功获取 data URL'
          images.value[0].url = dataUrl
          console.log('成功加载 data URL 图片')
          return true
        }

        // 如果上述方法都失败，尝试使用原始 URL
        const imageUrl = await window.electronAPI.getLocalImageUrl(imagePath)
        debugInfo.value += `\n获取到图片URL: ${imageUrl}`

        if (imageUrl) {
          // 更新图片URL
          images.value[0].url = imageUrl
          console.log('成功加载本地图片:', imageUrl)
          return true
        } else {
          console.error('无法获取本地图片URL')
          debugInfo.value += '\n无法获取本地图片URL'
          loadError.value = true
          images.value[0].url = 'src/assets/1.tiff'
          return false
        }
      } else {
        console.error('本地图片文件不存在:', imagePath)
        debugInfo.value += '\n本地图片文件不存在'
        loadError.value = true
        // 更新为错误图片
        images.value[0].url = 'src/assets/1.tiff'
        return false
      }
    } else {
      debugInfo.value += '\n非Electron环境，使用默认图片'
      console.log('非Electron环境，使用默认图片')
      return false
    }
  } catch (error) {
    console.error('加载图片时出错:', error)
    debugInfo.value += `\n加载图片时出错: ${error.message}`
    loadError.value = true
    images.value[0].url = 'https://via.placeholder.com/800x400/f8f9fa/6c757d?text=加载错误'
    return false
  } finally {
    isLoading.value = false
  }
}

// 获取并加载最新的第一张图片
async function fetchAndLoadLatestImage() {
  try {
    if (!window.electronAPI) return false

    console.log(`尝试获取第一个目录的最新图片: ${imageDirectoryPath.value}`)

    // 强制获取最新的TIFF文件，不使用缓存
    const latestImage = await window.electronAPI.getLatestTiffFile(imageDirectoryPath.value)

    if (latestImage) {
      console.log(`找到最新第一张图片: ${latestImage}`)
      latestDiscoveredImagePath.value = latestImage

      // 尝试清除图片缓存（如果有这个功能）
      try {
        if (window.electronAPI.clearImageCache) {
          await window.electronAPI.clearImageCache(latestImage)
          console.log(`清除图片缓存: ${latestImage}`)
        }
      } catch (e) {
        console.log('清除缓存失败，忽略错误')
      }

      // 更新路径变量
      console.log(`在 fetchAndLoadLatestImage 中更新第一张图片路径: ${latestImage}`)
      latestDiscoveredImagePath.value = latestImage

      // 保存到 Pinia store
      thicknessStore.saveLastImagePath(latestImage)

      // 立即保存当前状态（包括保存到 localStorage 和 Electron 主进程）
      await saveCurrentState()

      // 加载这个图片
      return await loadImage(latestImage)
    } else {
      console.log('没有找到第一张图片，使用默认路径')
      // 保存默认路径到 Pinia store
      thicknessStore.saveLastImagePath(localImagePath.value)
      // 如果没有找到最新图片，尝试使用默认路径
      return await loadImage(localImagePath.value)
    }
  } catch (error) {
    console.error('获取最新第一张图片时出错:', error)
    // 保存默认路径到 Pinia store
    thicknessStore.saveLastImagePath(localImagePath.value)
    // 如果出错，尝试使用默认路径
    return await loadImage(localImagePath.value)
  }
}

// 获取并加载最新的第二张图片
async function fetchAndLoadLatestImage2() {
  try {
    if (!window.electronAPI) return false

    console.log(`尝试获取第二个目录的最新图片: ${imageDirectoryPath2.value}`)

    // 对第二张图片使用宽松的模式，允许任何TIFF文件
    const latestImage = await window.electronAPI.getLatestImageFile(imageDirectoryPath2.value, '.*\\.(tiff?|tif)$')

    if (latestImage) {
      console.log(`找到最新第二张图片: ${latestImage}`)
      latestDiscoveredImagePath2.value = latestImage

      // 尝试清除图片缓存（如果有这个功能）
      try {
        if (window.electronAPI.clearImageCache) {
          await window.electronAPI.clearImageCache(latestImage)
          console.log(`清除图片缓存: ${latestImage}`)
        }
      } catch (e) {
        console.log('清除缓存失败，忽略错误')
      }

      // 更新路径变量
      console.log(`在 fetchAndLoadLatestImage2 中更新第二张图片路径: ${latestImage}`)
      latestDiscoveredImagePath2.value = latestImage

      // 保存到 Pinia store
      thicknessStore.saveLastImagePath2(latestImage)

      // 立即保存当前状态（包括保存到 localStorage 和 Electron 主进程）
      await saveCurrentState()

      // 加载这个图片
      return await loadImage2(latestImage)
    } else {
      console.log('没有找到第二张图片，使用默认路径')
      // 保存默认路径到 Pinia store
      thicknessStore.saveLastImagePath2(localImagePath2.value)
      // 如果没有找到最新图片，尝试使用默认路径
      return await loadImage2(localImagePath2.value)
    }
  } catch (error) {
    console.error('获取最新第二张图片时出错:', error)
    // 保存默认路径到 Pinia store
    thicknessStore.saveLastImagePath2(localImagePath2.value)
    // 如果出错，尝试使用默认路径
    return await loadImage2(localImagePath2.value)
  }
}

// 加载第二张图片的方法
async function loadImage2(imagePath) {
  try {
    isLoading.value = true
    loadError.value = false
    debugInfo.value = `开始加载第二张图片: ${imagePath}`

    // 提取时间步数并更新标题
    const extractedTimeStep = extractTimeStep(imagePath)
    if (extractedTimeStep) {
      timeStepValue2.value = extractedTimeStep
      currentTimeStep2.value = extractedTimeStep
      updateImageTitle2(extractedTimeStep)

      // 保存最后打开的图片路径和时间步数
      console.log(`保存第二张图片路径到 Pinia store: ${imagePath}`)
      console.log(`保存第二张图片时间步数到 Pinia store: ${extractedTimeStep}`)
      thicknessStore.saveLastImagePath2(imagePath)
      thicknessStore.saveLastTimeStep2(extractedTimeStep)

      // 验证保存是否成功
      console.log(`保存后的 localStorage 值: ${localStorage.getItem('last-image-path-2')}`)

      // 保存到 Electron 主进程
      if (window.electronAPI && window.electronAPI.saveImagePaths) {
        await window.electronAPI.saveImagePaths(
          latestDiscoveredImagePath.value,
          imagePath,
          currentTimeStep.value,
          extractedTimeStep
        )
        console.log('已将第二张图片路径保存到 Electron 主进程')
      }
    }

    // 检查是否在Electron环境中
    if (window.electronAPI) {
      // 检查文件是否存在
      const fileExists = await window.electronAPI.fileExists(imagePath)
      debugInfo.value += `\n文件存在: ${fileExists}`

      if (fileExists) {
        // 直接尝试使用 JPEG 格式
        debugInfo.value += '\n尝试使用 JPEG 格式加载图片...'
        const jpegDataUrl = await window.electronAPI.getLocalImageAsJpeg(imagePath)

        // 直接保存路径
        latestDiscoveredImagePath2.value = imagePath
        // 立即保存当前状态
        await saveCurrentState()

        if (jpegDataUrl) {
          debugInfo.value += '\n成功获取 JPEG 格式图片'
          images.value[1].url = jpegDataUrl
          console.log('成功加载 JPEG 格式图片')
          return true
        }

        // 如果 JPEG 失败，尝试使用普通的 data URL
        debugInfo.value += '\n尝试使用普通 data URL...'
        const dataUrl = await window.electronAPI.getImageDataUrl(imagePath)

        if (dataUrl) {
          debugInfo.value += '\n成功获取 data URL'
          images.value[1].url = dataUrl
          console.log('成功加载 data URL 图片')
          return true
        }

        // 如果上述方法都失败，尝试使用原始 URL
        const imageUrl = await window.electronAPI.getLocalImageUrl(imagePath)
        debugInfo.value += `\n获取到图片URL: ${imageUrl}`

        if (imageUrl) {
          // 更新图片URL
          images.value[1].url = imageUrl
          console.log('成功加载本地图片:', imageUrl)
          return true
        } else {
          console.error('无法获取本地图片URL')
          debugInfo.value += '\n无法获取本地图片URL'
          loadError.value = true
          images.value[1].url = 'src/assets/1.tiff'
          return false
        }
      } else {
        console.error('本地图片文件不存在:', imagePath)
        debugInfo.value += '\n本地图片文件不存在'
        loadError.value = true
        // 更新为错误图片
        images.value[1].url = 'src/assets/1.tiff'
        return false
      }
    } else {
      debugInfo.value += '\n非Electron环境，使用默认图片'
      console.log('非Electron环境，使用默认图片')
      return false
    }
  } catch (error) {
    console.error('加载第二张图片时出错:', error)
    debugInfo.value += `\n加载第二张图片时出错: ${error.message}`
    loadError.value = true
    images.value[1].url = 'https://via.placeholder.com/800x400/f8f9fa/6c757d?text=加载错误'
    return false
  } finally {
    isLoading.value = false
  }
}

// 开始监听图片目录
async function startWatchingImageDirectory() {
  if (!window.electronAPI) return

  try {
    // 先移除所有现有的监听器
    window.electronAPI.removeNewImageListener()

    // 开始监听第一张图片目录
    const success1 = await window.electronAPI.startWatchingDirectory(imageDirectoryPath.value)

    // 开始监听第二张图片目录
    const success2 = await window.electronAPI.startWatchingDirectory(imageDirectoryPath2.value)

    if (success1) {
      console.log(`开始监听第一张图片目录: ${imageDirectoryPath.value}`)
    } else {
      console.error('无法监听第一张图片目录')
    }

    if (success2) {
      console.log(`开始监听第二张图片目录: ${imageDirectoryPath2.value}`)
    } else {
      console.error('无法监听第二张图片目录')
    }

    // 只添加一个事件监听器，处理所有新图片
    window.electronAPI.onNewImageDetected(async (imagePath) => {
      console.log(`检测到新图片: ${imagePath}`)

      // 将路径转换为标准格式进行比较
      const normalizedPath = imagePath.replace(/\\/g, '/')
      const dir1 = imageDirectoryPath.value.replace(/\\/g, '/')
      const dir2 = imageDirectoryPath2.value.replace(/\\/g, '/')

      console.log(`标准化路径: ${normalizedPath}`)
      console.log(`第一个目录: ${dir1}`)
      console.log(`第二个目录: ${dir2}`)

      // 为第一张和第二张图片使用不同的文件名模式检查
      const pattern1 = /final_predicted_results_.*\.tiff?$/i  // 第一张图片：匹配 final_predicted_results_*.tiff
      const pattern2 = /.*\.tiff?$/i  // 第二张图片宽松模式，允许任何TIFF文件

      // 检查是否属于第一张图片目录
      if (normalizedPath.startsWith(dir1)) {
        // 第一张图片使用严格的文件名检查
        if (!pattern1.test(normalizedPath)) {
          console.log(`跳过非目标格式的第一张图片文件: ${normalizedPath}`)
          return
        }
        console.log(`该图片属于第一张图片目录: ${normalizedPath}`)

        // 尝试清除缓存
        try {
          if (window.electronAPI.clearImageCache) {
            await window.electronAPI.clearImageCache(imagePath)
            console.log(`清除图片缓存: ${imagePath}`)
          }
        } catch (e) {
          console.log('清除缓存失败，忽略错误')
        }

        // 强制刷新图片
        console.log(`强制刷新第一张图片: ${imagePath}`)
        latestDiscoveredImagePath.value = imagePath

        // 更新路径后立即保存当前状态
        await saveCurrentState()

        // 直接加载新图片，不清除当前图片
        // 设置一个加载中的占位图，使用原来的图片作为背景
        const currentUrl = images.value[0].url
        if (currentUrl && !currentUrl.includes('placeholder')) {
          // 保留当前图片，不显示红色背景
          loadImage(imagePath)
        } else {
          // 如果当前没有有效图片，显示加载中的占位图
          images.value[0].url = 'https://via.placeholder.com/800x400/cccccc/000000?text=加载中...'
          loadImage(imagePath)
        }
      }
      // 检查是否属于第二张图片目录
      else if (normalizedPath.startsWith(dir2)) {
        // 第二张图片使用宽松的文件名检查，允许任何TIFF文件
        if (!pattern2.test(normalizedPath)) {
          console.log(`跳过非TIFF格式的第二张图片文件: ${normalizedPath}`)
          return
        }
        console.log(`该图片属于第二张图片目录: ${normalizedPath}`)

        // 尝试清除缓存
        try {
          if (window.electronAPI.clearImageCache) {
            await window.electronAPI.clearImageCache(imagePath)
            console.log(`清除图片缓存: ${imagePath}`)
          }
        } catch (e) {
          console.log('清除缓存失败，忽略错误')
        }

        // 强制刷新图片
        console.log(`强制刷新第二张图片: ${imagePath}`)
        latestDiscoveredImagePath2.value = imagePath

        // 更新路径后立即保存当前状态
        await saveCurrentState()

        // 直接加载新图片，不清除当前图片
        // 设置一个加载中的占位图，使用原来的图片作为背景
        const currentUrl = images.value[1].url
        if (currentUrl && !currentUrl.includes('placeholder')) {
          // 保留当前图片，不显示红色背景
          loadImage2(imagePath)
        } else {
          // 如果当前没有有效图片，显示加载中的占位图
          images.value[1].url = 'https://via.placeholder.com/800x400/cccccc/000000?text=加载中...'
          loadImage2(imagePath)
        }
      } else {
        console.log(`该图片不属于任何监听目录: ${normalizedPath}`)
      }
    })

    // 添加文件删除事件监听器
    window.electronAPI.onImageDeleted(async (deletedImagePath) => {
      console.log(`检测到图片删除: ${deletedImagePath}`)

      // 将路径转换为标准格式进行比较
      const normalizedPath = deletedImagePath.replace(/\\/g, '/')
      const dir1 = imageDirectoryPath.value.replace(/\\/g, '/')
      const dir2 = imageDirectoryPath2.value.replace(/\\/g, '/')

      console.log(`删除的图片路径: ${normalizedPath}`)
      console.log(`第一个目录: ${dir1}`)
      console.log(`第二个目录: ${dir2}`)

      // 检查删除的图片是否是当前显示的图片
      const currentPath1 = latestDiscoveredImagePath.value?.replace(/\\/g, '/')
      const currentPath2 = latestDiscoveredImagePath2.value?.replace(/\\/g, '/')

      if (normalizedPath === currentPath1) {
        console.log('删除的是第一张图片，尝试加载最新图片')
        // 清除当前路径
        latestDiscoveredImagePath.value = null
        // 尝试获取并加载最新图片
        await fetchAndLoadLatestImage()
        // 如果没有找到新图片，显示占位图
        if (!latestDiscoveredImagePath.value) {
          images.value[0].url = 'https://via.placeholder.com/800x400/f0f0f0/666666?text=暂无图片'
          images.value[0].title = '暂无图片'
        }
      } else if (normalizedPath === currentPath2) {
        console.log('删除的是第二张图片，尝试加载最新图片')
        // 清除当前路径
        latestDiscoveredImagePath2.value = null
        // 尝试获取并加载最新图片
        await fetchAndLoadLatestImage2()
        // 如果没有找到新图片，显示占位图
        if (!latestDiscoveredImagePath2.value) {
          images.value[1].url = 'https://via.placeholder.com/800x400/f0f0f0/666666?text=暂无图片'
          images.value[1].title = '暂无图片'
        }
      }

      // 保存当前状态
      await saveCurrentState()
    })
  } catch (error) {
    console.error('监听目录时出错:', error)
  }
}
</script>

<style scoped>
.image-display {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 1.2rem;
  z-index: 2;
}

.error-overlay {
  background: rgba(220, 53, 69, 0.7);
}

/* .debug-info {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #17a2b8;
  font-family: monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
} */

.image-grid {
  display: flex;
  gap: 1.5rem;
  height: 400px;
  /* 增加高度，给图片更多空间 */
  margin-bottom: 1rem;
}

.image-card {
  position: relative;
  flex: 1;
  cursor: pointer;
  width: 100%;
  height: 75%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  display: flex;
  /* 使用 flex 布局 */
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  padding: 10px;
  /* 添加内边距 */
  background-color: white;
  /* 确保背景是白色 */
}

.image-card:hover {
  transform: scale(1.02);
}

.image-card.active {
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border: 2px solid #a5c6db;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.7) 100%);
  pointer-events: none;
}

/* .refresh-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
} */

/* .image-card:hover .refresh-button {
  opacity: 1;
}

.refresh-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
} */

.image-card img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* 改为 contain 确保图片完全显示 */
  background-color: #f8f9fa;
  /* 添加背景色，使图片边界更清晰 */
}

.image-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 10px;
  font-size: 1rem;
  font-weight: 500;
  z-index: 1;
  text-align: center;
  backdrop-filter: blur(2px);
  /* 添加模糊效果 */
  max-height: 40px;
  /* 限制标题高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-description {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3498db;
  min-height: 150px;
  transition: all 0.3s ease;
}

.image-description h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.image-description p {
  color: #555;
  line-height: 1.6;
  text-align: justify;
}

/* 响应式布局 */
/* 小屏幕优化 */
@media (max-width: 768px) {
  .image-card {
    height: 250px;
    margin-bottom: 1rem;
  }

  .image-card img {
    max-height: 200px;
    width: auto;
    max-width: 100%;
  }

  /* 介绍的高度 */
  .image-description {
    margin-top: -150px;
  }
}

/* 中等屏幕适配 (769px - 1199px) */
@media (min-width: 769px) and (max-width: 1199px) {
  .image-card {
    height: 50%;
  }

  .image-card img {
    max-height: 200px;
  }

  /* 介绍的高度 */
  .image-description {
    margin-top: -200px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .image-card img {
    max-height: 400px;
  }

  /* 介绍的高度 */
  .image-description {
    margin-top: -100px;
  }
}
</style>