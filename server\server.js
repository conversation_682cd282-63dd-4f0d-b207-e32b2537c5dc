const express = require('express');
const app = express();
const port = 3000;

// 中间件：允许跨域请求和解析JSON
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

app.use(express.json());

// 积液厚度计算API
app.get('/api/calculate', (req, res) => {
    // 从查询参数获取值
    const inclinationAngle = req.query.angle;
    const flowRate = req.query.flow;
    const waterContent = req.query.water;

    // 验证参数是否存在
    if (inclinationAngle === undefined || flowRate === undefined || waterContent === undefined) {
        return res.status(400).json({
            error: '缺少参数。请提供inclinationAngle, flowRate, waterContent'
        });
    }

    // 转换为数值
    const x1 = parseFloat(inclinationAngle);
    const x2 = parseFloat(flowRate);
    const x3 = parseFloat(waterContent);

    // 验证数值有效性
    if (isNaN(x1) || isNaN(x2) || isNaN(x3)) {
        return res.status(400).json({
            error: '参数无效。请提供有效的数值'
        });
    }

    // 使用给定公式计算积液厚度
    const thickness = calculateThickness(x1, x2, x3);

    // 返回计算结果
    res.json({ thickness });
});

// 计算函数（与前端相同的算法）
function calculateThickness(x1, x2, x3) {
    return 0.5121 +
        3.475 * x1 +
        2.1715 * x2 +
        5648.7847 * x3 -
        0.7712 * x1 * x2 +
        655.5461 * x1 * x3 -
        1179.5383 * x2 * x3 -
        0.0542 * Math.pow(x1, 2) -
        0.4452 * Math.pow(x2, 2) -
        330811.8291 * Math.pow(x3, 2);
}


// 临界流速计算API
app.get('/api/calculateF', (req, res) => {
    try {
        // 从查询参数获取值
        const inclinationAngle = req.query.angle;
        const thickNess = req.query.thick;
        const waterContent = req.query.water;

        // 验证参数是否存在
        if (inclinationAngle === undefined || thickNess === undefined || waterContent === undefined) {
            return res.status(400).json({
                error: '缺少参数。请提供inclinationAngle, thickNess, waterContent'
            });
        }

        // 转换为数值
        const x1 = parseFloat(inclinationAngle);
        const y = parseFloat(thickNess);
        const x3 = parseFloat(waterContent);

        // 验证数值有效性
        if (isNaN(x1) || isNaN(y) || isNaN(x3)) {
            return res.status(400).json({
                error: '参数无效。请提供有效的数值'
            });
        }

        // 验证参数范围
        if (x3 < 0 || x3 > 1) {
            return res.status(400).json({
                error: '含水量必须在0到1之间'
            });
        }

        if (y <= 0) {
            return res.status(400).json({
                error: '积液厚度必须大于0'
            });
        }

        // 使用给定公式计算流速
        const flowRate = calculateFlowRate(x1, y, x3);

        // 返回计算结果
        res.json({ flowRate });

    } catch (error) {
        console.error('计算流速时出错:', error);
        return res.status(400).json({
            error: error.message
        });
    }
});

// 计算函数（与前端相同的算法）
// 计算管道流速的方法
const calculateFlowRate = (x1, y, x3) => {

    const a = -0.4452
    const b = 2.1715 - 0.7712 * x1 - 1179.5383 * x3
    const c = 0.5121 + 3.475 * x1 + 5648.7847 * x3 + 655.5461 * x1 * x3 - 0.0542 * Math.pow(x1, 2) - 330811.8291 * Math.pow(x3, 2) - y

    // 计算判别式
    const discriminant = Math.pow(b, 2) - 4 * a * c

    if (discriminant < 0) {
        throw new Error('无实数解，请检查输入参数')
    }

    // 使用二次方程求根公式
    const x2_1 = (-b + Math.sqrt(discriminant)) / (2 * a)
    const x2_2 = (-b - Math.sqrt(discriminant)) / (2 * a)

    // 选择合理的解（通常选择正值，因为流速应该为正）
    let flowRate
    if (x2_1 > 0 && x2_2 > 0) {
        // 如果两个解都为正，选择较小的那个（通常更合理）
        flowRate = Math.min(x2_1, x2_2)
    } else if (x2_1 > 0) {
        flowRate = x2_1
    } else if (x2_2 > 0) {
        flowRate = x2_2
    } 

    return parseFloat(flowRate.toFixed(4))

}
// 启动服务
app.listen(port, '0.0.0.0', () => {
    console.log(`后端服务运行在 http://localhost:${port}`);
    console.log(`积液厚度计算API端点: http://localhost:${port}/api/calculate?angle=2&flow=0.0003&water=0.009`);
    console.log(`管道流速计算API端点: http://localhost:${port}/api/calculateF?angle=2&thick=0.0003&water=0.009`);
});