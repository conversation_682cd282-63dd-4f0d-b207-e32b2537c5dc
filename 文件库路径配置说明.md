# 文件库路径配置说明

## 📁 如何修改文件库显示的图片路径

文件库功能已经实现，图片路径已经写死在代码中，用户无法通过界面修改。如果您需要更改要显示的图片目录，请按以下步骤操作：

### 🔧 修改步骤

1. **打开文件**: `src/components/ImageLibrary.vue`

2. **找到路径配置部分** (大约在第163-184行):

```javascript
// 写死的图片目录路径配置
const imageDirectories = [
  {
    name: 'CFD预测器应用目录',
    path: 'C:\\Program Files\\CFD_Grey_Markov_Predictor\\application',
    description: 'CFD预测器主应用目录'
  },
  {
    name: '用户临时目录1',
    path: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\YFL\\mcrCache9.14\\run_pr0\\run_predicti\\Predicted_Results',
    description: '第一个预测结果目录'
  },
  {
    name: '用户临时目录2', 
    path: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\YFL\\test',
    description: '第二个测试目录'
  },
  {
    name: '桌面图片目录',
    path: 'C:\\Users\\<USER>\\Desktop\\Images',
    description: '桌面图片文件夹'
  }
]
```

3. **修改路径**: 根据您的需要修改上述配置中的路径

### 📝 配置说明

每个目录配置包含三个字段：

- **name**: 目录的显示名称（会在图片信息中显示）
- **path**: 实际的文件系统路径（使用双反斜杠 `\\` 或正斜杠 `/`）
- **description**: 目录的描述信息（会在图片预览中显示）

### 🎯 路径格式示例

```javascript
// Windows 路径格式（推荐使用双反斜杠）
'C:\\Program Files\\MyApp\\images'

// 或者使用正斜杠（也支持）
'C:/Program Files/MyApp/images'

// 用户目录示例
'C:\\Users\\<USER>\\Documents\\Pictures'

// 网络路径示例
'\\\\server\\share\\images'
```

### 🖼️ 支持的图片格式

当前支持的图片格式在 `supportedExtensions` 数组中定义：

```javascript
// 支持的文件扩展名
const supportedExtensions = ['tiff', 'jpg', 'jpeg', 'png']
```

如需添加其他格式，请修改此数组。

### 🔄 应用更改

修改路径后，需要：

1. 保存文件
2. 重新构建应用: `npm run build`
3. 重新启动应用

### 📋 当前配置的目录

目前配置了以下4个目录：

1. **CFD预测器应用目录**: `C:\Program Files\CFD_Grey_Markov_Predictor\application`
2. **用户临时目录1**: `C:\Users\<USER>\AppData\Local\Temp\YFL\mcrCache9.14\run_pr0\run_predicti\Predicted_Results`
3. **用户临时目录2**: `C:\Users\<USER>\AppData\Local\Temp\YFL\test`
4. **桌面图片目录**: `C:\Users\<USER>\Desktop\Images`

### ⚠️ 注意事项

1. **路径必须存在**: 确保指定的路径在文件系统中存在
2. **权限问题**: 确保应用有权限访问指定的目录
3. **路径格式**: Windows 路径中的反斜杠需要转义（使用双反斜杠 `\\`）
4. **用户名变量**: 如果路径中包含用户名，建议直接写死具体的用户名

### 🚀 功能特点

- **多目录支持**: 可以同时扫描多个目录
- **自动排序**: 图片按修改时间排序（最新的在前）
- **目录标识**: 每个图片都会显示来源目录
- **格式过滤**: 只显示支持的图片格式
- **错误处理**: 自动跳过无法访问的目录

---

如有任何问题，请检查控制台日志获取详细的错误信息。
