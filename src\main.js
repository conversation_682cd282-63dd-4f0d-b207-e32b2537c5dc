import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import App from './App.vue'

// 导入自定义样式
import './assets/electron-styles.css'

// 添加调试信息
console.log('Vue 应用程序正在初始化...')

// 检查DOM是否准备好
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM已加载完成')
    console.log('查找挂载点:', document.getElementById('app'))
})

// 捕获全局错误
window.addEventListener('error', (event) => {
    console.error('捕获到全局错误:', event.error)
})

// 捕获未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason)
})

try {
    console.log('创建Vue应用实例')
    const app = createApp(App)
    const pinia = createPinia()

    // 注册Element Plus
    console.log('注册插件')
    app.use(ElementPlus)
    app.use(pinia)

    // 添加错误处理
    app.config.errorHandler = (err, vm, info) => {
        console.error('Vue错误处理器捕获到错误:', err)
        console.error('错误信息:', info)
    }

    console.log('挂载应用到DOM')
    app.mount('#app')
    console.log('应用挂载完成')
} catch (error) {
    console.error('Vue应用初始化失败:', error)
}
