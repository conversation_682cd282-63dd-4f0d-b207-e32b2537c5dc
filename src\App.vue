<script setup>
import { ref } from 'vue'
import InputForm from './components/InputForm.vue'
import InputForm2 from './components/InputForm2.vue'
import ThicknessChart from './components/ThicknessChart.vue'
import ThicknessChart2 from './components/ThicknessChart2.vue'
import ImageDisplay from './components/ImageDisplay.vue'
import ImageLibrary from './components/ImageLibrary.vue'
import { useThicknessStore } from './stores/thicknessStore'
// 暂时使用emoji图标，避免Element Plus图标导入问题

// 积液厚度数据
const thicknessValue = ref(0)
// 流速数据
const flowRateValue = ref(0)
// 使用store
const thicknessStore = useThicknessStore()

// 当前选择的计算模式
const calculationMode = ref('select') // 'select', 'mode1', 'mode2', 'library'

// 更新积液厚度数据的方法
const updateThicknessData = (value) => {
  thicknessValue.value = value
}

// 更新流速数据的方法
const updateFlowRateData = (value) => {
  flowRateValue.value = value
}

// 选择计算模式的方法
const selectMode = (mode) => {
  calculationMode.value = mode
}

// 返回选择界面
const backToSelection = () => {
  calculationMode.value = 'select'
}

// 打开文件库的方法
const openImageLibrary = () => {
  calculationMode.value = 'library'
}

// 从文件库返回的方法
const backFromLibrary = () => {
  calculationMode.value = 'select'
}


// 获取当前用户计算机用户名
// 检查是否在Electron环境中
if (window.electronAPI) {
  fetchUsername()
}
else {
  console.log('非Electron环境，无法获取用户名')
}
async function fetchUsername() {
  try {
    const username = await window.electronAPI.getUsername()
    thicknessStore.addUserName(username)
    console.log('当前用户名:', thicknessStore.username)
  } catch (error) {
    console.error('获取用户名失败:', error)
    return '未知用户'
  }
}
// fetchUsername()
</script>

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <h1>数据可视化分析系统</h1>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 计算模式选择界面 -->
      <div v-if="calculationMode === 'select'" class="mode-selection">
        <div class="selection-container">
          <h2>请选择计算模式</h2>
          <div class="mode-cards">
            <div class="mode-card" @click="selectMode('mode1')">
              <div class="card-icon">
                📊
              </div>
              <h3>计算积液厚度</h3>
              <p class="card-description">
                输入：管道倾斜角、管内流速、管内含水量<br>
                输出：积液厚度
              </p>
              <div class="card-formula">
                y = f(x₁, x₂, x₃)
              </div>
            </div>

            <div class="mode-card" @click="selectMode('mode2')">
              <div class="card-icon">
                ⚙️
              </div>
              <h3>计算管道流速</h3>
              <p class="card-description">
                输入：管道倾斜角、积液厚度、管内含水量<br>
                输出：管道流速
              </p>
              <div class="card-formula">
                x₂ = f(x₁, y, x₃)
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模式1：计算积液厚度 -->
      <template v-else-if="calculationMode === 'mode1'">
        <!-- 左侧输入区域 -->
        <div class="input-section">
          <div class="mode-header">
            <el-button @click="backToSelection" type="text" class="back-button">
              ← 返回选择
            </el-button>
            <h2>计算积液厚度</h2>
          </div>
          <InputForm @update-chart="updateThicknessData" />
        </div>

        <!-- 右侧展示区域 -->
        <div class="display-section">
          <div class="chart-container">
            <h2>积液厚度数据</h2>
            <ThicknessChart :thickness="thicknessValue" />
          </div>
          <div class="image-container">
            <div class="analysis-header">
              <h2>数据分析</h2>
              <el-button type="primary" @click="openImageLibrary" class="library-button">
                文件库
              </el-button>
            </div>
            <ImageDisplay />
          </div>
        </div>
      </template>

      <!-- 模式2：计算管道流速 -->
      <template v-else-if="calculationMode === 'mode2'">
        <!-- 左侧输入区域 -->
        <div class="input-section">
          <div class="mode-header">
            <el-button @click="backToSelection" type="text" class="back-button">
              ← 返回选择
            </el-button>
            <h2>计算临界流速</h2>
          </div>
          <InputForm2 @update-chart="updateFlowRateData" />
        </div>

        <!-- 右侧展示区域 -->
        <div class="display-section">
          <div class="chart-container">
            <h2>临界流速计算结果</h2>
            <ThicknessChart2 :flowRate="flowRateValue" />
          </div>
          <div class="image-container">
            <div class="analysis-header">
              <h2>数据分析</h2>
              <el-button type="primary" @click="openImageLibrary" class="library-button">
                文件库
              </el-button>
            </div>
            <ImageDisplay />
          </div>
        </div>
      </template>

      <!-- 文件库模式 -->
      <template v-else-if="calculationMode === 'library'">
        <div class="library-section">
          <ImageLibrary @back="backFromLibrary" />
        </div>
      </template>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <p>© 2025 数据可视化分析系统 - 版权所有</p>
    </footer>
  </div>
</template>

<style>
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

#app {
  height: 100vh;
  width: 100%;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  padding: 3px;
}

/* 顶部导航栏样式 */
.header {
  background: linear-gradient(135deg, #3498db, #2c3e50);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px 10px 0 0;
}

/* 主内容区域样式 */
.main-content {
  display: flex;
  flex: 1;
  padding: 2rem;
  gap: 2rem;
  height: calc(100vh - 120px);
  /* 减去头部和底部的高度 */
}

/* 左侧输入区域 */
.input-section {
  flex: 1;
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  /* 允许垂直滚动 */
  height: 100%;
}

/* 右侧展示区域 */
.display-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow-y: auto;
  /* 允许垂直滚动 */
  height: 100%;
}

.chart-container,
.image-container {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

/* 数据分析头部样式 */
.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.analysis-header h2 {
  margin-bottom: 0;
}

.library-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.library-button:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* 文件库区域样式 */
.library-section {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

/* 页脚样式 */
.footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 1rem;
  margin-top: auto;
  border-radius: 0 0 10px 10px;
}

h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

/* 模式选择界面样式 */
.mode-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 10px;
}

.selection-container {
  text-align: center;
  max-width: 800px;
  width: 100%;
  padding: 2rem;
}

.selection-container h2 {
  font-size: 2rem;
  margin-bottom: 3rem;
  color: #2c3e50;
  border: none;
  padding: 0;
}

.mode-cards {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

.mode-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  width: 300px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.mode-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
}

.card-icon {
  color: #3498db;
  margin-bottom: 1rem;
  font-size: 48px;
}

.mode-card h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  border: none;
  padding: 0;
}

.card-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.card-formula {
  background: #f8f9fa;
  padding: 0.8rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #3498db;
  border: 1px solid #e9ecef;
}

/* 模式头部样式 */
.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.back-button {
  color: #3498db !important;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background-color: #f0f8ff !important;
  color: #2980b9 !important;
}

.mode-header h2 {
  margin: 0;
  border: none;
  padding: 0;
}
</style>
