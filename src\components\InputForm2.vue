<template>
    <div class="input-form">
        <el-form :model="formData">
            <el-form-item label="管道倾斜角(°)：">
                <el-input v-model="formData.inclinationAngle" type="number" placeholder="请输入管道倾斜角"></el-input>
            </el-form-item>

            <el-form-item label="积液厚度(mm)：">
                <el-input v-model="formData.thickness" type="number" placeholder="请输入积液厚度"></el-input>
            </el-form-item>

            <el-form-item label="管内含水量(%)：">
                <el-input v-model="formData.waterContent" type="number" placeholder="请输入含水量（0-1之间）"></el-input>
                <span class="input-tip">请输入0到1之间的小数，例如：0.25表示25%</span>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="calculateFlowRate">计算管道流速</el-button>
                <el-button @click="resetForm">重置</el-button>
            </el-form-item>
        </el-form>

        <div class="result-panel" v-if="showResult">
            <h3>计算结果</h3>
            <p class="result-value">临界流速：{{ calculatedFlowRate }} m/s</p>
            <div class="formula-explanation">
                <h4>计算公式：</h4>
                <p>x₂ = -0.4452x₂² + (2.1715 - 0.7712x₁ - 1179.5383x₃)x₂ + (0.5121 + 3.475x₁ + 5648.7847x₃ +
                    655.5461x₁x₃ - 0.0542x₁² - 330811.8291x₃² - y) = 0</p>
                <p>其中：x₁为管道倾斜角，y为积液厚度，x₃为管内含水量</p>
            </div>

            <div class="action-buttons">
                <el-button size="small" type="danger" @click="clearHistoryAndRefresh">
                    清空历史记录
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useFlowRateStore } from '../stores/flowRateStore'

// 使用store
const flowRateStore = useFlowRateStore()

// 定义表单数据
const formData = reactive({
    inclinationAngle: '', // 管道倾斜角 x₁
    thickness: '',        // 积液厚度 y
    waterContent: ''      // 管内含水量 x₃
})

// 计算结果相关变量
const calculatedFlowRate = ref(0)
const showResult = ref(false)

// 清空历史记录并刷新页面的方法
const clearHistoryAndRefresh = () => {
    // 先清空历史记录
    flowRateStore.clearHistory()
    // 然后刷新页面
    window.location.reload()
}

// 计算管道流速的方法
// 方式一 前端处理
const calculateFlowRate = () => {
    // 获取输入值
    const x1 = parseFloat(formData.inclinationAngle)  // 管道倾斜角
    const y = parseFloat(formData.thickness)          // 积液厚度
    const x3 = parseFloat(formData.waterContent)      // 管内含水量

    // 验证输入
    if (isNaN(x1) || isNaN(y) || isNaN(x3)) {
        alert('请输入有效的数值')
        return
    }

    // 将原公式整理为关于x₂的二次方程：ax₂² + bx₂ + c = 0
    // 原公式：y = 0.5121 + 3.475x₁ + 2.1715x₂ + 5648.7847x₃ - 0.7712x₁x₂ + 655.5461x₁x₃ - 1179.5383x₂x₃ - 0.0542x₁² - 0.4452x₂² - 330811.8291x₃²
    // 重新整理：-0.4452x₂² + (2.1715 - 0.7712x₁ - 1179.5383x₃)x₂ + (0.5121 + 3.475x₁ + 5648.7847x₃ + 655.5461x₁x₃ - 0.0542x₁² - 330811.8291x₃² - y) = 0

    const a = -0.4452
    const b = 2.1715 - 0.7712 * x1 - 1179.5383 * x3
    const c = 0.5121 + 3.475 * x1 + 5648.7847 * x3 + 655.5461 * x1 * x3 - 0.0542 * Math.pow(x1, 2) - 330811.8291 * Math.pow(x3, 2) - y

    // 计算判别式
    const discriminant = Math.pow(b, 2) - 4 * a * c

    if (discriminant < 0) {
        alert('无实数解，请检查输入参数')
        return
    }

    // 使用二次方程求根公式
    const x2_1 = (-b + Math.sqrt(discriminant)) / (2 * a)
    const x2_2 = (-b - Math.sqrt(discriminant)) / (2 * a)

    // 选择合理的解（通常选择正值，因为流速应该为正）
    let flowRate
    if (x2_1 > 0 && x2_2 > 0) {
        // 如果两个解都为正，选择较小的那个（通常更合理）
        flowRate = Math.min(x2_1, x2_2)
    } else if (x2_1 > 0) {
        flowRate = x2_1
    } else if (x2_2 > 0) {
        flowRate = x2_2
    } else {
        alert('计算结果无合理的正值解，请检查输入参数')
        return
    }

    // 保留4位小数
    calculatedFlowRate.value = parseFloat(flowRate.toFixed(4))

    showResult.value = true

    // 触发事件，通知父组件更新图表
    emit('update-chart', flowRate)
}
// 方式二 后端处理
// const calculateFlowRate = async() => {
//     try {
//         // 获取输入值
//         const x1 = parseFloat(formData.inclinationAngle)  // 管道倾斜角
//         const y = parseFloat(formData.thickness)          // 积液厚度
//         const x3 = parseFloat(formData.waterContent)      // 管内含水量

//         // 验证输入
//         if (isNaN(x1) || isNaN(y) || isNaN(x3)) {
//             ElMessage.error('请输入有效的数值')
//             return
//         }

//         // 验证含水量范围
//         if (x3 < 0 || x3 > 1) {
//             ElMessage.error('含水量必须在0到1之间')
//             return
//         }

//         // 验证积液厚度为正数
//         if (y <= 0) {
//             ElMessage.error('积液厚度必须大于0')
//             return
//         }

//         // 后端API地址
//         // 构建URL参数
//         const params = new URLSearchParams({
//             angle: x1,
//             thick: y,
//             water: x3
//         });

//         const response = await fetch(`http://localhost:3000/api/calculateF?${params}`);

//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`)
//         }

//         const data = await response.json();

//         if (data.error) {
//             ElMessage.error(data.error)
//             return
//         }

//         // 从后端返回的数据中提取流速值
//         calculatedFlowRate.value = data.flowRate

//         showResult.value = true

//         // 触发事件，通知父组件更新图表
//         emit('update-chart', data.flowRate)

//     } catch (error) {
//         console.error('计算错误:', error)
//         ElMessage.error('计算过程中出现错误，请检查输入或稍后重试')
//         // 确保在错误时不显示结果
//         showResult.value = false
//     }
// }

// 重置表单
const resetForm = () => {
    Object.keys(formData).forEach(key => {
        formData[key] = ''
    })
    showResult.value = false
}

// 定义组件事件
const emit = defineEmits(['update-chart'])
</script>

<style scoped>
.input-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.result-panel {
    background: #edf7fd;
    border-left: 4px solid #3498db;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
}

.result-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #3498db;
    margin-top: 0.5rem;
}

h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.input-tip {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.2rem;
}

.formula-explanation {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px dashed #bbb;
}

.formula-explanation h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.formula-explanation p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #555;
}

.action-buttons {
    margin-top: 1rem;
    text-align: right;
}
</style>