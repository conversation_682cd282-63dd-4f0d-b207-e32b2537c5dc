import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThicknessStore = defineStore('thickness', {
  state: () => {
    // 从localStorage获取数据，如果没有则使用默认值
    const savedData = localStorage.getItem('thickness-history')
    const initialData = savedData ? JSON.parse(savedData) : []

    // 获取最后打开的第一张图片路径
    const lastImagePath = localStorage.getItem('last-image-path') || ''

    // 获取第一张图片的时间步数
    const lastTimeStep = localStorage.getItem('last-time-step') || '2'

    // 获取最后打开的第二张图片路径
    const lastImagePath2 = localStorage.getItem('last-image-path-2') || ''

    // 获取第二张图片的时间步数
    const lastTimeStep2 = localStorage.getItem('last-time-step-2') || '2'

    //存储当前用户计算机的用户名
    const username = ref('')
    return {
      // 积液厚度历史数据
      thicknessHistory: initialData,
      // 最大显示数据条数
      maxHistoryItems: 7,
      // 当前用户计算机的用户名
      username,
      // 最后打开的第一张图片路径
      lastImagePath,
      // 第一张图片的时间步数
      lastTimeStep,
      // 最后打开的第二张图片路径
      lastImagePath2,
      // 第二张图片的时间步数
      lastTimeStep2
    }
  },

  actions: {
    // 获取当前用户计算机的用户名
    addUserName(username) {
      this.username = username
    },
    // 添加新的积液厚度数据
    addThicknessData(value) {
      // 获取当前日期
      const now = new Date()
      const formattedDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`

      // 创建新数据项
      const newItem = {
        time: formattedDate,
        value: parseFloat(value.toFixed(2))
      }

      // 添加到历史记录
      this.thicknessHistory.push(newItem)

      // 如果超过最大限制，删除最早的数据
      if (this.thicknessHistory.length > this.maxHistoryItems) {
        this.thicknessHistory.shift()
      }

      // 保存到localStorage
      this._saveToLocalStorage()
    },

    // 清空历史数据
    clearHistory() {
      this.thicknessHistory = []

      // 保存到localStorage
      this._saveToLocalStorage()
    },

    // 保存第一张图片的路径
    saveLastImagePath(imagePath) {
      console.log(`thicknessStore: 保存第一张图片路径: ${imagePath}`)
      this.lastImagePath = imagePath
      localStorage.setItem('last-image-path', imagePath)
      console.log(`thicknessStore: 保存后的 localStorage 值: ${localStorage.getItem('last-image-path')}`)
    },

    // 保存第一张图片的时间步数
    saveLastTimeStep(timeStep) {
      this.lastTimeStep = timeStep
      localStorage.setItem('last-time-step', timeStep)
    },

    // 保存第二张图片的路径
    saveLastImagePath2(imagePath) {
      console.log(`thicknessStore: 保存第二张图片路径: ${imagePath}`)
      this.lastImagePath2 = imagePath
      localStorage.setItem('last-image-path-2', imagePath)
      console.log(`thicknessStore: 保存后的 localStorage 值: ${localStorage.getItem('last-image-path-2')}`)
    },

    // 保存第二张图片的时间步数
    saveLastTimeStep2(timeStep) {
      this.lastTimeStep2 = timeStep
      localStorage.setItem('last-time-step-2', timeStep)
    },

    // 私有方法：保存到localStorage
    _saveToLocalStorage() {
      localStorage.setItem('thickness-history', JSON.stringify(this.thicknessHistory))
    }
  }
})