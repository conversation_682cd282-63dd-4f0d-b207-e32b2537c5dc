{"name": "electron-test", "productName": "Sein", "private": true, "version": "1.0.4", "scripts": {"start": "electron-forge start", "dev": "vite", "build": "vue-tsc && vite build && electron-builder", "build:win": "vue-tsc && vite build && electron-builder --win --publish=never --config.asar=true --config.files=[\"./**/*\"] --config.directories.output=\"release\"", "build:simple": "vue-tsc && vite build && electron .", "build:dir": "vue-tsc && vite build && electron-builder --dir", "build:mac": "vue-tsc && vite build && electron-builder --mac", "build:linux": "vue-tsc && vite build && electron-builder --linux", "preview": "vite preview", "package": "electron-forge package", "make": "electron-forge make"}, "author": "xmr001", "license": "ISC", "description": "electron project", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "chokidar": "^4.0.3", "cnpm": "^9.4.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "express": "^5.1.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "sharp": "^0.34.1", "utif": "^3.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@electron-forge/cli": "^7.8.0", "@vitejs/plugin-vue": "^5.2.1", "electron": "^30.0.1", "electron-builder": "^26.0.12", "nodemon": "^3.1.9", "typescript": "^5.2.2", "vite": "^6.2.0", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^2.0.26"}, "main": "electron/main.js"}